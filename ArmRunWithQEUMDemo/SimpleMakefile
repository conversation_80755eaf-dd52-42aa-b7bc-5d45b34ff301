# Simple Makefile for iOS ARM Hotfix Engine Core Test
# Minimal build system for testing core functionality

CC = clang
CFLAGS = -Wall -Wextra -std=c99 -O2

# Simple test target
simple-test: SimpleTest.c
	@echo "🔨 Compiling simple core test..."
	@$(CC) $(CFLAGS) SimpleTest.c -o simple-test
	@echo "✅ Compilation successful!"
	@echo ""
	@echo "🧪 Running simple core test..."
	@./simple-test

# Clean target
clean-simple:
	@echo "🧹 Cleaning simple test files..."
	@rm -f simple-test
	@echo "✅ Clean completed!"

# Help target
help-simple:
	@echo "Simple Test Build System"
	@echo "========================"
	@echo ""
	@echo "Available targets:"
	@echo "  simple-test    - Build and run simple core test"
	@echo "  clean-simple   - Clean simple test files"
	@echo "  help-simple    - Show this help"
	@echo ""
	@echo "This is a minimal test of the core ARM interpretation logic"
	@echo "without complex dependencies or Objective-C frameworks."

.PHONY: simple-test clean-simple help-simple
