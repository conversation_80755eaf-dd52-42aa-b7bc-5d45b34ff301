# 快速开始指南

## 🚀 立即验证核心功能

如果您想快速验证项目的核心ARM解释执行功能，请运行以下命令：

```bash
cd ArmRunWithQEUMDemo
make -f SimpleMakefile simple-test
```

这将编译并运行一个简化的核心测试，验证：
- TCI（TCG解释器）基本操作
- ARM Thumb指令解码
- ARM到TCI的指令翻译
- 完整的执行流程
- 性能基准测试

## 📊 预期输出

成功运行后，您应该看到类似以下的输出：

```
🚀 iOS ARM Hotfix Engine - Simple Core Test
============================================

🧪 Testing TCI Basic Operations...
✅ PASSED: TCI initialization
✅ PASSED: MOV instruction execution
✅ PASSED: MOV result verification
✅ PASSED: ADD instruction execution
✅ PASSED: ADD result verification
✅ PASSED: SHL instruction execution
✅ PASSED: SHL result verification (5 << 2 = 20)

🧪 Testing ARM Instruction Decoding...
✅ PASSED: Thumb MOV decoding
✅ PASSED: MOV opcode correct
✅ PASSED: MOV destination register correct
✅ PASSED: MOV immediate value correct
✅ PASSED: Thumb ADD decoding
✅ PASSED: ADD opcode correct
✅ PASSED: ADD destination register correct
✅ PASSED: ADD source register 1 correct
✅ PASSED: ADD source register 2 correct

🧪 Testing ARM to TCI Translation...
✅ PASSED: All ARM instructions translated
✅ PASSED: TCI execution completed successfully
✅ PASSED: R0 = 10
✅ PASSED: R1 = 20
✅ PASSED: R2 = 30 (10 + 20)
✅ PASSED: R3 = 60 (30 << 1)

🧪 Testing Performance...
   ⏱️  Executed 1000 instruction sequences in 0.000 seconds
   📊 Average time per sequence: 0.000 ms
✅ PASSED: Performance test completed within 1 second

🏁 Test Results:
   Total Tests: 4
   Passed: 4
   Failed: 0
   Success Rate: 100.0%
🎉 All tests passed!
```

## 🔧 如果遇到问题

### 编译错误
如果遇到编译错误，请确保：
1. 已安装Xcode Command Line Tools：`xcode-select --install`
2. clang编译器可用：`which clang`

### 运行错误
如果程序无法运行，请检查：
1. 文件权限：`ls -la simple-test`
2. 系统兼容性：确保在macOS系统上运行

## 📁 测试的核心功能

### 1. TCI基本操作
- **MOV指令**：`mov r0, #42` - 将立即数42移动到寄存器R0
- **ADD指令**：`add r3, r1, r2` - 将R1和R2相加，结果存入R3
- **SHL指令**：`lsl r5, r4, #2` - 将R4左移2位，结果存入R5

### 2. ARM指令解码
- **Thumb MOV**：`0x2000 | (reg << 8) | imm` - 解码Thumb模式MOV指令
- **Thumb ADD**：`0x1800 | (rm << 6) | (rn << 3) | rd` - 解码Thumb模式ADD指令
- **Thumb LSL**：`0x0000 | (imm << 6) | (rm << 3) | rd` - 解码Thumb模式LSL指令

### 3. 完整执行流程
测试执行以下ARM代码序列：
```assembly
mov r0, #10    ; R0 = 10
mov r1, #20    ; R1 = 20  
add r2, r0, r1 ; R2 = R0 + R1 = 30
lsl r3, r2, #1 ; R3 = R2 << 1 = 60
```

### 4. 性能测试
- 执行1000次指令序列
- 测量总执行时间
- 计算平均每条指令的执行时间

## 🎯 核心技术验证

这个简化测试成功验证了项目的核心技术方案：

✅ **ARM指令解释执行**：能够正确解码和执行ARM Thumb指令  
✅ **TCI解释器**：基于QEMU TCI的字节码解释执行  
✅ **指令翻译**：ARM指令到TCI字节码的正确翻译  
✅ **寄存器管理**：虚拟寄存器的正确读写操作  
✅ **执行流程**：完整的解码→翻译→执行流程  
✅ **性能表现**：毫秒级的指令执行性能  

## 📚 下一步

验证核心功能后，您可以：

1. **查看完整文档**：阅读 `README.md` 了解完整功能
2. **探索源代码**：查看 `SimpleTest.c` 了解实现细节
3. **尝试完整项目**：使用Xcode打开完整的iOS项目
4. **自定义测试**：修改测试代码，添加更多ARM指令支持

## 🏆 成功标志

如果您看到 "🎉 All tests passed!" 消息，恭喜您！这意味着：

- 项目的核心ARM解释执行引擎工作正常
- ARM指令解码逻辑正确
- TCI解释器功能完整
- 整个技术方案是可行的

这为在iOS平台上实现ARM代码热修复奠定了坚实的技术基础！
