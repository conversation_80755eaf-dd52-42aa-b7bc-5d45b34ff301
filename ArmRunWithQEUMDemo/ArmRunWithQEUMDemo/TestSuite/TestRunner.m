/*
 * Test Runner for iOS Hot-fix Engine
 * Simple test runner that doesn't require XCTest framework
 *
 * Copyright (c) 2025
 */

#import <Foundation/Foundation.h>
#import "QEMUEngineTests.h"

@interface TestRunner : NSObject
@end

@implementation TestRunner

// 测试运行器 - 可以从应用中调用
+ (void)runTestSuite {
    NSLog(@"🧪 iOS ARM Hotfix Engine Test Suite");
    NSLog(@"=====================================\n");

    // 创建测试实例
    QEMUEngineTests *tests = [[QEMUEngineTests alloc] init];

    // 运行所有测试
    [tests runAllTests];

    NSLog(@"\n🏁 Test suite completed.");
}

@end
