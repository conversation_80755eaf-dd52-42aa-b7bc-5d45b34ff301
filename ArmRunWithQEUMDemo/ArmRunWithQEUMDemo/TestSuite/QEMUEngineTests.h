/*
 * QEMU Engine Tests Header for iOS Hot-fix Engine
 * Simple test suite for QEMU components and ARM interpretation
 * (Non-XCTest version for standalone testing)
 *
 * Copyright (c) 2025
 */

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class QEMUBridge;
@class ARMInterpreter;
@class HotfixManager;
@class MethodReplacer;

/**
 * Simple test suite for the iOS ARM Hotfix Engine
 * This class provides comprehensive testing without requiring XCTest framework
 */
@interface QEMUEngineTests : NSObject

// Core components
@property (nonatomic, strong) QEMUBridge *qemuBridge;
@property (nonatomic, strong) ARMInterpreter *armInterpreter;
@property (nonatomic, strong) HotfixManager *hotfixManager;
@property (nonatomic, strong) MethodReplacer *methodReplacer;

/**
 * Run all available tests
 * This method will execute all test cases and provide a summary
 */
- (void)runAllTests;

/**
 * Setup method called before each test
 */
- (void)setUp;

/**
 * Teardown method called after each test
 */
- (void)tearDown;

// Individual test methods
- (BOOL)testQEMUBridgeInitialization;
- (BOOL)testQEMUBridgeThumbMOVInstruction;
- (BOOL)testQEMUBridgeThumbADDInstruction;
- (BOOL)testARMInterpreterInitialization;
- (BOOL)testARMInstructionParsing;
- (BOOL)testARMCodeBlockParsing;
- (BOOL)testHotfixManagerInitialization;
- (BOOL)testHotfixPatchLoading;
- (BOOL)testMethodReplacerInitialization;
- (BOOL)testMethodExistenceCheck;
- (BOOL)testSafetyChecks;
- (BOOL)testFullHotfixWorkflow;
- (BOOL)testPerformance;

@end

NS_ASSUME_NONNULL_END
