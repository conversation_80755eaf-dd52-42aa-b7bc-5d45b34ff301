/*
 * QEMU Engine Tests for iOS Hot-fix Engine
 * Simple test suite for QEMU components and ARM interpretation
 * (Non-XCTest version for standalone testing)
 *
 * Copyright (c) 2025
 */

#import <Foundation/Foundation.h>
#import "../HotfixEngine/QEMUBridge.h"
#import "../HotfixEngine/ARMInterpreter.h"
#import "../HotfixEngine/HotfixManager.h"
#import "../HotfixEngine/MethodReplacer.h"

// 简单的测试断言宏
#define TEST_ASSERT(condition, message) \
    do { \
        if (!(condition)) { \
            NSLog(@"❌ ASSERTION FAILED: %@ - %s:%d", message, __FILE__, __LINE__); \
            return NO; \
        } else { \
            NSLog(@"✅ PASSED: %@", message); \
        } \
    } while(0)

#define TEST_ASSERT_EQUAL(actual, expected, message) \
    do { \
        if ((actual) != (expected)) { \
            NSLog(@"❌ ASSERTION FAILED: %@ - Expected: %ld, Actual: %ld - %s:%d", \
                  message, (long)(expected), (long)(actual), __FILE__, __LINE__); \
            return NO; \
        } else { \
            NSLog(@"✅ PASSED: %@", message); \
        } \
    } while(0)

#define TEST_ASSERT_NOT_NIL(object, message) \
    do { \
        if ((object) == nil) { \
            NSLog(@"❌ ASSERTION FAILED: %@ - Object is nil - %s:%d", message, __FILE__, __LINE__); \
            return NO; \
        } else { \
            NSLog(@"✅ PASSED: %@", message); \
        } \
    } while(0)

#define TEST_ASSERT_NIL(object, message) \
    do { \
        if ((object) != nil) { \
            NSLog(@"❌ ASSERTION FAILED: %@ - Object is not nil - %s:%d", message, __FILE__, __LINE__); \
            return NO; \
        } else { \
            NSLog(@"✅ PASSED: %@", message); \
        } \
    } while(0)

@interface QEMUEngineTests : NSObject
@property (nonatomic, strong) QEMUBridge *qemuBridge;
@property (nonatomic, strong) ARMInterpreter *armInterpreter;
@property (nonatomic, strong) HotfixManager *hotfixManager;
@property (nonatomic, strong) MethodReplacer *methodReplacer;

// 测试方法
- (void)runAllTests;
- (BOOL)testQEMUBridgeInitialization;
- (BOOL)testQEMUBridgeThumbMOVInstruction;
- (BOOL)testQEMUBridgeThumbADDInstruction;
- (BOOL)testARMInterpreterInitialization;
- (BOOL)testARMInstructionParsing;
- (BOOL)testARMCodeBlockParsing;
- (BOOL)testHotfixManagerInitialization;
- (BOOL)testHotfixPatchLoading;
- (BOOL)testMethodReplacerInitialization;
- (BOOL)testMethodExistenceCheck;
- (BOOL)testSafetyChecks;
- (BOOL)testFullHotfixWorkflow;
- (BOOL)testPerformance;

@end

@implementation QEMUEngineTests

- (instancetype)init {
    self = [super init];
    if (self) {
        self.qemuBridge = [QEMUBridge sharedInstance];
        self.armInterpreter = [ARMInterpreter sharedInstance];
        self.hotfixManager = [HotfixManager sharedManager];
        self.methodReplacer = [MethodReplacer sharedInstance];
    }
    return self;
}

- (void)setUp {
    // 清理之前的状态
    [self.qemuBridge cleanup];
    [self.armInterpreter cleanup];
    [self.hotfixManager cleanup];
    [self.methodReplacer cleanup];
}

- (void)tearDown {
    [self.qemuBridge cleanup];
    [self.armInterpreter cleanup];
    [self.hotfixManager cleanup];
    [self.methodReplacer cleanup];
}

- (void)runAllTests {
    NSLog(@"🚀 Starting QEMU Engine Tests...\n");

    int totalTests = 0;
    int passedTests = 0;

    // 运行所有测试
    NSArray *testMethods = @[
        @"testQEMUBridgeInitialization",
        @"testQEMUBridgeThumbMOVInstruction",
        @"testQEMUBridgeThumbADDInstruction",
        @"testARMInterpreterInitialization",
        @"testARMInstructionParsing",
        @"testARMCodeBlockParsing",
        @"testHotfixManagerInitialization",
        @"testHotfixPatchLoading",
        @"testMethodReplacerInitialization",
        @"testMethodExistenceCheck",
        @"testSafetyChecks",
        @"testFullHotfixWorkflow",
        @"testPerformance"
    ];

    for (NSString *testName in testMethods) {
        NSLog(@"\n📋 Running test: %@", testName);
        [self setUp]; // 每个测试前重置状态

        SEL testSelector = NSSelectorFromString(testName);
        if ([self respondsToSelector:testSelector]) {
            totalTests++;

            // 使用NSInvocation调用测试方法
            NSMethodSignature *signature = [self methodSignatureForSelector:testSelector];
            NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:signature];
            [invocation setTarget:self];
            [invocation setSelector:testSelector];
            [invocation invoke];

            // 获取返回值
            BOOL result = NO;
            [invocation getReturnValue:&result];

            if (result) {
                passedTests++;
                NSLog(@"✅ %@ PASSED", testName);
            } else {
                NSLog(@"❌ %@ FAILED", testName);
            }
        }

        [self tearDown]; // 每个测试后清理
    }

    NSLog(@"\n🏁 Test Results:");
    NSLog(@"   Total Tests: %d", totalTests);
    NSLog(@"   Passed: %d", passedTests);
    NSLog(@"   Failed: %d", totalTests - passedTests);
    NSLog(@"   Success Rate: %.1f%%", totalTests > 0 ? (float)passedTests / totalTests * 100 : 0);

    if (passedTests == totalTests) {
        NSLog(@"🎉 All tests passed!");
    } else {
        NSLog(@"⚠️  Some tests failed. Please check the logs above.");
    }
}

#pragma mark - QEMU Bridge Tests

- (BOOL)testQEMUBridgeInitialization {
    BOOL success = [self.qemuBridge initializeEngine];
    TEST_ASSERT(success, @"QEMU bridge should initialize successfully");
    TEST_ASSERT([self.qemuBridge isInitialized], @"QEMU bridge should be initialized");
    return YES;
}

- (BOOL)testQEMUBridgeThumbMOVInstruction {
    // 初始化QEMU引擎
    BOOL initSuccess = [self.qemuBridge initializeEngine];
    TEST_ASSERT(initSuccess, @"QEMU bridge initialization failed");

    // 创建Thumb MOV指令：mov r0, #42
    uint16_t instruction = 0x2000 | (0 << 8) | 42; // mov r0, #42
    NSData *codeData = [NSData dataWithBytes:&instruction length:sizeof(uint16_t)];

    // 设置执行选项
    QEMUExecutionOptions *options = [[QEMUExecutionOptions alloc] init];
    options.thumbMode = YES;
    options.maxInstructions = 1;

    // 执行指令
    id result = nil;
    QEMUExecutionResult execResult = [self.qemuBridge executeARMCode:codeData
                                                         withOptions:options
                                                              result:&result];

    TEST_ASSERT_EQUAL(execResult, QEMUExecutionResultSuccess, @"Thumb MOV instruction should execute successfully");

    // 验证R0寄存器的值
    uint32_t r0Value = [self.qemuBridge getRegisterValue:QEMUARMRegisterR0];
    TEST_ASSERT_EQUAL(r0Value, 42, @"R0 register should contain value 42");

    return YES;
}

- (BOOL)testQEMUBridgeThumbADDInstruction {
    BOOL initSuccess = [self.qemuBridge initializeEngine];
    TEST_ASSERT(initSuccess, @"QEMU bridge initialization failed");

    // 设置初始寄存器值
    [self.qemuBridge setRegisterValue:QEMUARMRegisterR1 value:10];
    [self.qemuBridge setRegisterValue:QEMUARMRegisterR2 value:20];

    // 创建Thumb ADD指令：add r0, r1, r2
    uint16_t instruction = 0x1800 | (2 << 6) | (1 << 3) | 0; // add r0, r1, r2
    NSData *codeData = [NSData dataWithBytes:&instruction length:sizeof(uint16_t)];

    QEMUExecutionOptions *options = [[QEMUExecutionOptions alloc] init];
    options.thumbMode = YES;

    id result = nil;
    QEMUExecutionResult execResult = [self.qemuBridge executeARMCode:codeData
                                                         withOptions:options
                                                              result:&result];

    TEST_ASSERT_EQUAL(execResult, QEMUExecutionResultSuccess, @"Thumb ADD instruction should execute successfully");

    uint32_t r0Value = [self.qemuBridge getRegisterValue:QEMUARMRegisterR0];
    TEST_ASSERT_EQUAL(r0Value, 30, @"R0 register should contain value 30 (10+20)");

    return YES;
}

#pragma mark - ARM Interpreter Tests

- (BOOL)testARMInterpreterInitialization {
    BOOL success = [self.armInterpreter initialize];
    TEST_ASSERT(success, @"ARM interpreter should initialize successfully");
    TEST_ASSERT([self.armInterpreter isInitialized], @"ARM interpreter should be initialized");
    return YES;
}

- (BOOL)testARMInstructionParsing {
    BOOL initSuccess = [self.armInterpreter initialize];
    TEST_ASSERT(initSuccess, @"ARM interpreter initialization failed");

    // 测试Thumb MOV指令解析
    uint32_t instruction = 0x2000 | (0 << 8) | 42; // mov r0, #42

    NSError *error = nil;
    ARMInstructionInfo *info = [self.armInterpreter parseInstruction:instruction
                                                           thumbMode:YES
                                                               error:&error];

    TEST_ASSERT_NOT_NIL(info, @"Instruction parsing should succeed");
    TEST_ASSERT_NIL(error, @"No error should occur during parsing");
    TEST_ASSERT_EQUAL(info.type, ARMInstructionTypeMOV, @"Instruction type should be MOV");
    TEST_ASSERT_EQUAL(info.destinationRegister, 0, @"Destination register should be R0");
    TEST_ASSERT_EQUAL(info.immediateValue, 42, @"Immediate value should be 42");
    TEST_ASSERT(info.hasImmediate, @"Instruction should have immediate value");

    return YES;
}

- (BOOL)testARMCodeBlockParsing {
    BOOL initSuccess = [self.armInterpreter initialize];
    TEST_ASSERT(initSuccess, @"ARM interpreter initialization failed");

    // 创建包含多条指令的代码块
    uint16_t instructions[] = {
        0x2000 | (0 << 8) | 10,  // mov r0, #10
        0x2000 | (1 << 8) | 20,  // mov r1, #20
        0x1800 | (1 << 6) | (0 << 3) | 2  // add r2, r0, r1
    };

    NSData *codeData = [NSData dataWithBytes:instructions length:sizeof(instructions)];

    NSError *error = nil;
    ARMCodeBlock *codeBlock = [self.armInterpreter parseCodeBlock:codeData
                                                        thumbMode:YES
                                                      baseAddress:0x1000
                                                            error:&error];

    TEST_ASSERT_NOT_NIL(codeBlock, @"Code block parsing should succeed");
    TEST_ASSERT_NIL(error, @"No error should occur during parsing");
    TEST_ASSERT_EQUAL(codeBlock.instructionCount, 3, @"Code block should contain 3 instructions");
    TEST_ASSERT(codeBlock.thumbMode, @"Code block should be in Thumb mode");
    TEST_ASSERT_EQUAL(codeBlock.baseAddress, 0x1000, @"Base address should be 0x1000");

    return YES;
}

#pragma mark - Hotfix Manager Tests

- (BOOL)testHotfixManagerInitialization {
    HotfixConfiguration *config = [[HotfixConfiguration alloc] init];
    config.enableDebugMode = YES;
    config.enableSafeMode = YES;

    BOOL success = [self.hotfixManager initializeWithConfiguration:config];
    TEST_ASSERT(success, @"Hotfix manager should initialize successfully");
    TEST_ASSERT([self.hotfixManager isInitialized], @"Hotfix manager should be initialized");

    return YES;
}

- (BOOL)testHotfixPatchLoading {
    HotfixConfiguration *config = [[HotfixConfiguration alloc] init];
    BOOL initSuccess = [self.hotfixManager initializeWithConfiguration:config];
    TEST_ASSERT(initSuccess, @"Hotfix manager initialization failed");

    // 创建测试补丁数据
    NSDictionary *patchDict = @{
        @"patchId": @"test-patch-001",
        @"version": @"1.0.0",
        @"targetVersion": @"1.0.0",
        @"description": @"Test patch for unit testing",
        @"operations": @[
            @{
                @"type": @"methodReplace",
                @"targetClass": @"TestClass",
                @"targetMethod": @"testMethod",
                @"armCode": @"", // Base64编码的ARM代码
                @"thumbMode": @YES
            }
        ]
    };

    NSError *jsonError = nil;
    NSData *patchData = [NSJSONSerialization dataWithJSONObject:patchDict
                                                        options:0
                                                          error:&jsonError];
    TEST_ASSERT_NOT_NIL(patchData, @"Patch data creation should succeed");
    TEST_ASSERT_NIL(jsonError, @"No JSON error should occur");

    HotfixPatchInfo *patchInfo = nil;
    NSError *loadError = nil;
    BOOL loadSuccess = [self.hotfixManager loadPatchFromData:patchData
                                                   patchInfo:&patchInfo
                                                       error:&loadError];

    TEST_ASSERT(loadSuccess, @"Patch loading should succeed");
    TEST_ASSERT_NOT_NIL(patchInfo, @"Patch info should be created");
    TEST_ASSERT_NIL(loadError, @"No load error should occur");
    TEST_ASSERT([patchInfo.patchId isEqualToString:@"test-patch-001"], @"Patch ID should match");
    TEST_ASSERT_EQUAL(patchInfo.status, HotfixPatchStatusValidated, @"Patch should be validated");

    return YES;
}

#pragma mark - Method Replacer Tests

- (BOOL)testMethodReplacerInitialization {
    BOOL success = [self.methodReplacer initialize];
    TEST_ASSERT(success, @"Method replacer should initialize successfully");
    TEST_ASSERT([self.methodReplacer isInitialized], @"Method replacer should be initialized");
    return YES;
}

- (BOOL)testMethodExistenceCheck {
    BOOL initSuccess = [self.methodReplacer initialize];
    TEST_ASSERT(initSuccess, @"Method replacer initialization failed");

    // 测试已知存在的方法
    BOOL exists = [self.methodReplacer isMethodExists:@selector(description)
                                               inClass:[NSObject class]];
    TEST_ASSERT(exists, @"description method should exist in NSObject");

    // 测试不存在的方法
    SEL nonExistentSelector = NSSelectorFromString(@"nonExistentMethod");
    BOOL notExists = [self.methodReplacer isMethodExists:nonExistentSelector
                                                  inClass:[NSObject class]];
    TEST_ASSERT(!notExists, @"nonExistentMethod should not exist");

    return YES;
}

- (BOOL)testSafetyChecks {
    BOOL initSuccess = [self.methodReplacer initialize];
    TEST_ASSERT(initSuccess, @"Method replacer initialization failed");

    // 测试不安全的类
    BOOL isSafe = [self.methodReplacer isSafeToReplace:@selector(description)
                                               inClass:[NSObject class]];
    TEST_ASSERT(!isSafe, @"NSObject methods should not be safe to replace");

    // 测试不安全的方法 (使用字符串避免ARC限制)
    SEL deallocSelector = NSSelectorFromString(@"dealloc");
    BOOL isDealloc = [self.methodReplacer isSafeToReplace:deallocSelector
                                                  inClass:[self class]];
    TEST_ASSERT(!isDealloc, @"dealloc method should not be safe to replace");

    return YES;
}

#pragma mark - Integration Tests

- (BOOL)testFullHotfixWorkflow {
    // 这是一个集成测试，测试完整的热修复流程

    // 1. 初始化所有组件
    TEST_ASSERT([self.qemuBridge initializeEngine], @"QEMU bridge initialization failed");
    TEST_ASSERT([self.armInterpreter initialize], @"ARM interpreter initialization failed");
    TEST_ASSERT([self.methodReplacer initialize], @"Method replacer initialization failed");

    HotfixConfiguration *config = [[HotfixConfiguration alloc] init];
    config.enableDebugMode = YES;
    TEST_ASSERT([self.hotfixManager initializeWithConfiguration:config], @"Hotfix manager initialization failed");

    // 2. 验证所有组件都已初始化
    TEST_ASSERT([self.qemuBridge isInitialized], @"QEMU bridge should be initialized");
    TEST_ASSERT([self.armInterpreter isInitialized], @"ARM interpreter should be initialized");
    TEST_ASSERT([self.methodReplacer isInitialized], @"Method replacer should be initialized");
    TEST_ASSERT([self.hotfixManager isInitialized], @"Hotfix manager should be initialized");

    // 3. 测试ARM代码执行
    uint16_t instruction = 0x2000 | (0 << 8) | 100; // mov r0, #100
    NSData *codeData = [NSData dataWithBytes:&instruction length:sizeof(uint16_t)];

    QEMUExecutionOptions *options = [[QEMUExecutionOptions alloc] init];
    options.thumbMode = YES;

    id result = nil;
    QEMUExecutionResult execResult = [self.qemuBridge executeARMCode:codeData
                                                         withOptions:options
                                                              result:&result];

    TEST_ASSERT_EQUAL(execResult, QEMUExecutionResultSuccess, @"ARM code execution should succeed");

    uint32_t r0Value = [self.qemuBridge getRegisterValue:QEMUARMRegisterR0];
    TEST_ASSERT_EQUAL(r0Value, 100, @"R0 register should contain value 100");

    // 4. 获取统计信息
    QEMUExecutionStats *stats = [self.qemuBridge getExecutionStats];
    TEST_ASSERT_NOT_NIL(stats, @"Execution stats should be available");
    TEST_ASSERT(stats.thumbMode, @"Stats should indicate Thumb mode");

    return YES;
}

- (BOOL)testPerformance {
    // 性能测试
    NSLog(@"   🏃‍♂️ Running performance test...");

    // 初始化QEMU引擎
    TEST_ASSERT([self.qemuBridge initializeEngine], @"QEMU bridge initialization failed");

    NSDate *startTime = [NSDate date];

    // 执行100次简单的ARM指令（减少数量以避免超时）
    for (int i = 0; i < 100; i++) {
        uint16_t instruction = 0x2000 | (0 << 8) | (i % 256); // mov r0, #i
        NSData *codeData = [NSData dataWithBytes:&instruction length:sizeof(uint16_t)];

        QEMUExecutionOptions *options = [[QEMUExecutionOptions alloc] init];
        options.thumbMode = YES;

        id result = nil;
        QEMUExecutionResult execResult = [self.qemuBridge executeARMCode:codeData
                                                             withOptions:options
                                                                  result:&result];

        if (execResult != QEMUExecutionResultSuccess) {
            NSLog(@"   ⚠️  Instruction %d failed", i);
        }
    }

    NSTimeInterval executionTime = [[NSDate date] timeIntervalSinceDate:startTime];
    NSLog(@"   ⏱️  Executed 100 instructions in %.3f seconds", executionTime);
    NSLog(@"   📊 Average time per instruction: %.3f ms", (executionTime * 1000) / 100);

    // 验证性能在合理范围内（每条指令不超过100ms）
    TEST_ASSERT(executionTime < 10.0, @"Performance test should complete within 10 seconds");

    return YES;
}

@end
