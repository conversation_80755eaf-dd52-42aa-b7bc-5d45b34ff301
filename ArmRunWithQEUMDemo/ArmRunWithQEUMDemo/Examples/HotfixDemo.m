/*
 * Hotfix Demo for iOS Hot-fix Engine
 * Demonstrates ARM code execution and method replacement
 *
 * Copyright (c) 2025
 */

#import <Foundation/Foundation.h>
#import "../HotfixEngine/QEMUBridge.h"
#import "../HotfixEngine/ARMInterpreter.h"
#import "../HotfixEngine/HotfixManager.h"
#import "../HotfixEngine/MethodReplacer.h"

// 示例类用于演示方法替换
@interface DemoClass : NSObject
- (NSInteger)calculateSum:(NSInteger)a b:(NSInteger)b;
- (NSString *)formatMessage:(NSString *)message;
- (void)performComplexOperation;
@end

@implementation DemoClass

- (NSInteger)calculateSum:(NSInteger)a b:(NSInteger)b {
    NSLog(@"Original calculateSum called with a=%ld, b=%ld", (long)a, (long)b);
    return a + b;
}

- (NSString *)formatMessage:(NSString *)message {
    NSLog(@"Original formatMessage called with: %@", message);
    return [NSString stringWithFormat:@"[Original] %@", message];
}

- (void)performComplexOperation {
    NSLog(@"Original performComplexOperation called");
    // 模拟复杂操作
    for (int i = 0; i < 1000; i++) {
        // 一些计算
    }
    NSLog(@"Original performComplexOperation completed");
}

@end

@interface HotfixDemo : NSObject
@property (nonatomic, strong) QEMUBridge *qemuBridge;
@property (nonatomic, strong) ARMInterpreter *armInterpreter;
@property (nonatomic, strong) HotfixManager *hotfixManager;
@property (nonatomic, strong) MethodReplacer *methodReplacer;
@end

@implementation HotfixDemo

- (instancetype)init {
    self = [super init];
    if (self) {
        _qemuBridge = [QEMUBridge sharedInstance];
        _armInterpreter = [ARMInterpreter sharedInstance];
        _hotfixManager = [HotfixManager sharedManager];
        _methodReplacer = [MethodReplacer sharedInstance];
    }
    return self;
}

- (void)runDemo {
    NSLog(@"=== iOS ARM Hotfix Engine Demo ===\n");
    
    [self initializeComponents];
    [self demonstrateBasicARMExecution];
    [self demonstrateInstructionParsing];
    [self demonstrateCodeBlockExecution];
    [self demonstrateHotfixPatchLoading];
    [self demonstrateMethodReplacement];
    [self demonstratePerformanceMonitoring];
    [self cleanup];
    
    NSLog(@"=== Demo Completed ===");
}

- (void)initializeComponents {
    NSLog(@"1. Initializing Components...");
    
    // 初始化QEMU引擎
    BOOL qemuSuccess = [self.qemuBridge initializeEngine];
    NSLog(@"   QEMU Bridge: %@", qemuSuccess ? @"✓ Initialized" : @"✗ Failed");
    
    // 初始化ARM解释器
    BOOL armSuccess = [self.armInterpreter initialize];
    NSLog(@"   ARM Interpreter: %@", armSuccess ? @"✓ Initialized" : @"✗ Failed");
    
    // 初始化方法替换器
    BOOL replacerSuccess = [self.methodReplacer initialize];
    NSLog(@"   Method Replacer: %@", replacerSuccess ? @"✓ Initialized" : @"✗ Failed");
    
    // 初始化热修复管理器
    HotfixConfiguration *config = [[HotfixConfiguration alloc] init];
    config.enableDebugMode = YES;
    config.enableSafeMode = YES;
    BOOL hotfixSuccess = [self.hotfixManager initializeWithConfiguration:config];
    NSLog(@"   Hotfix Manager: %@", hotfixSuccess ? @"✓ Initialized" : @"✗ Failed");
    
    NSLog(@"");
}

- (void)demonstrateBasicARMExecution {
    NSLog(@"2. Basic ARM Instruction Execution...");
    
    // 演示Thumb MOV指令：mov r0, #42
    uint16_t movInstruction = 0x2000 | (0 << 8) | 42;
    NSData *movCode = [NSData dataWithBytes:&movInstruction length:sizeof(uint16_t)];
    
    QEMUExecutionOptions *options = [[QEMUExecutionOptions alloc] init];
    options.thumbMode = YES;
    options.enableStats = YES;
    
    id result = nil;
    QEMUExecutionResult execResult = [self.qemuBridge executeARMCode:movCode
                                                         withOptions:options
                                                              result:&result];
    
    if (execResult == QEMUExecutionResultSuccess) {
        uint32_t r0Value = [self.qemuBridge getRegisterValue:QEMUARMRegisterR0];
        NSLog(@"   ✓ MOV R0, #42 executed successfully");
        NSLog(@"   ✓ R0 register value: %u", r0Value);
    } else {
        NSLog(@"   ✗ MOV instruction failed: %@", [self.qemuBridge getLastError]);
    }
    
    // 演示Thumb ADD指令：add r2, r0, r1
    [self.qemuBridge setRegisterValue:QEMUARMRegisterR0 value:10];
    [self.qemuBridge setRegisterValue:QEMUARMRegisterR1 value:20];
    
    uint16_t addInstruction = 0x1800 | (1 << 6) | (0 << 3) | 2; // add r2, r0, r1
    NSData *addCode = [NSData dataWithBytes:&addInstruction length:sizeof(uint16_t)];
    
    execResult = [self.qemuBridge executeARMCode:addCode withOptions:options result:&result];
    
    if (execResult == QEMUExecutionResultSuccess) {
        uint32_t r2Value = [self.qemuBridge getRegisterValue:QEMUARMRegisterR2];
        NSLog(@"   ✓ ADD R2, R0, R1 executed successfully");
        NSLog(@"   ✓ R2 register value: %u (10 + 20)", r2Value);
    } else {
        NSLog(@"   ✗ ADD instruction failed: %@", [self.qemuBridge getLastError]);
    }
    
    NSLog(@"");
}

- (void)demonstrateInstructionParsing {
    NSLog(@"3. ARM Instruction Parsing...");
    
    // 解析MOV指令
    uint32_t movInstruction = 0x2000 | (0 << 8) | 100; // mov r0, #100
    
    NSError *error = nil;
    ARMInstructionInfo *info = [self.armInterpreter parseInstruction:movInstruction
                                                           thumbMode:YES
                                                               error:&error];
    
    if (info && !error) {
        NSLog(@"   ✓ Instruction parsed successfully:");
        NSLog(@"     Type: %@", [ARMInterpreter instructionTypeName:info.type]);
        NSLog(@"     Mnemonic: %@", info.mnemonic);
        NSLog(@"     Operands: %@", info.operands);
        NSLog(@"     Destination Register: R%ld", (long)info.destinationRegister);
        NSLog(@"     Immediate Value: %u", info.immediateValue);
        NSLog(@"     Has Immediate: %@", info.hasImmediate ? @"YES" : @"NO");
    } else {
        NSLog(@"   ✗ Instruction parsing failed: %@", error.localizedDescription);
    }
    
    NSLog(@"");
}

- (void)demonstrateCodeBlockExecution {
    NSLog(@"4. Code Block Execution...");
    
    // 创建包含多条指令的代码块
    uint16_t instructions[] = {
        0x2000 | (0 << 8) | 5,   // mov r0, #5
        0x2000 | (1 << 8) | 10,  // mov r1, #10
        0x1800 | (1 << 6) | (0 << 3) | 2,  // add r2, r0, r1
        0x0000 | (1 << 6) | (2 << 3) | 3   // lsl r3, r2, #1 (multiply by 2)
    };
    
    NSData *codeData = [NSData dataWithBytes:instructions length:sizeof(instructions)];
    
    NSError *error = nil;
    ARMCodeBlock *codeBlock = [self.armInterpreter parseCodeBlock:codeData
                                                        thumbMode:YES
                                                      baseAddress:0x1000
                                                            error:&error];
    
    if (codeBlock && !error) {
        NSLog(@"   ✓ Code block parsed successfully:");
        NSLog(@"     Instructions: %ld", (long)codeBlock.instructionCount);
        NSLog(@"     Base Address: 0x%X", codeBlock.baseAddress);
        NSLog(@"     Thumb Mode: %@", codeBlock.thumbMode ? @"YES" : @"NO");
        
        // 执行代码块
        QEMUExecutionOptions *options = [[QEMUExecutionOptions alloc] init];
        options.thumbMode = YES;
        
        ARMExecutionContext *context = [self.armInterpreter executeCodeBlock:codeBlock
                                                                  withOptions:options
                                                                        error:&error];
        
        if (context && !error) {
            NSLog(@"   ✓ Code block executed successfully:");
            NSLog(@"     Executed Instructions: %ld", (long)context.executedInstructions);
            NSLog(@"     Execution Time: %.3f ms", context.executionTime * 1000);
            
            // 显示寄存器状态
            QEMUARMState *finalState = context.finalState;
            if (finalState && finalState.registers.count >= 4) {
                NSLog(@"     Final Register Values:");
                NSLog(@"       R0: %@", finalState.registers[0]);
                NSLog(@"       R1: %@", finalState.registers[1]);
                NSLog(@"       R2: %@", finalState.registers[2]);
                NSLog(@"       R3: %@", finalState.registers[3]);
            }
        } else {
            NSLog(@"   ✗ Code block execution failed: %@", error.localizedDescription);
        }
    } else {
        NSLog(@"   ✗ Code block parsing failed: %@", error.localizedDescription);
    }
    
    NSLog(@"");
}

- (void)demonstrateHotfixPatchLoading {
    NSLog(@"5. Hotfix Patch Loading...");
    
    // 创建示例补丁
    NSDictionary *patchDict = @{
        @"patchId": @"demo-patch-001",
        @"version": @"1.0.0",
        @"targetVersion": @"1.0.0",
        @"description": @"Demo patch for calculateSum method optimization",
        @"metadata": @{
            @"author": @"Hotfix Engine Demo",
            @"priority": @"high"
        },
        @"operations": @[
            @{
                @"type": @"methodReplace",
                @"targetClass": @"DemoClass",
                @"targetMethod": @"calculateSum:b:",
                @"armCode": @"", // 实际应用中这里是Base64编码的ARM代码
                @"thumbMode": @YES,
                @"parameters": @{
                    @"optimization": @"fast_math"
                }
            }
        ]
    };
    
    NSError *jsonError = nil;
    NSData *patchData = [NSJSONSerialization dataWithJSONObject:patchDict
                                                        options:NSJSONWritingPrettyPrinted
                                                          error:&jsonError];
    
    if (patchData && !jsonError) {
        HotfixPatchInfo *patchInfo = nil;
        NSError *loadError = nil;
        BOOL loadSuccess = [self.hotfixManager loadPatchFromData:patchData
                                                       patchInfo:&patchInfo
                                                           error:&loadError];
        
        if (loadSuccess && patchInfo) {
            NSLog(@"   ✓ Patch loaded successfully:");
            NSLog(@"     Patch ID: %@", patchInfo.patchId);
            NSLog(@"     Version: %@", patchInfo.version);
            NSLog(@"     Description: %@", patchInfo.description);
            NSLog(@"     Status: %ld", (long)patchInfo.status);
            NSLog(@"     Operations: %ld", (long)patchInfo.operations.count);
            NSLog(@"     Checksum: %@", patchInfo.checksum);
        } else {
            NSLog(@"   ✗ Patch loading failed: %@", loadError.localizedDescription);
        }
    } else {
        NSLog(@"   ✗ Patch data creation failed: %@", jsonError.localizedDescription);
    }
    
    NSLog(@"");
}

- (void)demonstrateMethodReplacement {
    NSLog(@"6. Method Replacement Demo...");
    
    // 创建测试对象
    DemoClass *demo = [[DemoClass alloc] init];
    
    // 调用原始方法
    NSLog(@"   Original method behavior:");
    NSInteger originalResult = [demo calculateSum:15 b:25];
    NSLog(@"     calculateSum(15, 25) = %ld", (long)originalResult);
    
    NSString *originalMessage = [demo formatMessage:@"Hello World"];
    NSLog(@"     formatMessage result: %@", originalMessage);
    
    // 检查方法替换的安全性
    BOOL isSafe = [self.methodReplacer isSafeToReplace:@selector(calculateSum:b:)
                                               inClass:[DemoClass class]];
    NSLog(@"   Method replacement safety check: %@", isSafe ? @"✓ Safe" : @"✗ Unsafe");
    
    if (isSafe) {
        // 注意：在实际应用中，这里应该使用真实的ARM代码
        // 这里只是演示接口调用
        NSData *dummyARMCode = [@"dummy_arm_code" dataUsingEncoding:NSUTF8StringEncoding];
        
        NSError *replaceError = nil;
        BOOL replaceSuccess = [self.methodReplacer replaceMethod:@selector(calculateSum:b:)
                                                         inClass:[DemoClass class]
                                                    withARMCode:dummyARMCode
                                                      thumbMode:YES
                                                          error:&replaceError];
        
        if (replaceSuccess) {
            NSLog(@"   ✓ Method replacement registered successfully");
            
            // 检查替换状态
            BOOL isReplaced = [self.methodReplacer isMethodReplaced:@selector(calculateSum:b:)
                                                            inClass:[DemoClass class]];
            NSLog(@"   ✓ Method replacement status: %@", isReplaced ? @"Replaced" : @"Original");
            
            // 获取替换信息
            MethodReplacementInfo *info = [self.methodReplacer getReplacementInfo:@selector(calculateSum:b:)
                                                                           inClass:[DemoClass class]];
            if (info) {
                NSLog(@"   ✓ Replacement info:");
                NSLog(@"     Class: %@", info.className);
                NSLog(@"     Method: %@", info.methodName);
                NSLog(@"     Type: %ld", (long)info.type);
                NSLog(@"     Active: %@", info.isActive ? @"YES" : @"NO");
                NSLog(@"     Replaced Date: %@", info.replacedDate);
            }
        } else {
            NSLog(@"   ✗ Method replacement failed: %@", replaceError.localizedDescription);
        }
    }
    
    NSLog(@"");
}

- (void)demonstratePerformanceMonitoring {
    NSLog(@"7. Performance Monitoring...");
    
    // 获取QEMU执行统计
    QEMUExecutionStats *qemuStats = [self.qemuBridge getExecutionStats];
    if (qemuStats) {
        NSLog(@"   QEMU Execution Statistics:");
        NSLog(@"     Instructions Executed: %ld", (long)qemuStats.instructionsExecuted);
        NSLog(@"     Translated Instructions: %ld", (long)qemuStats.translatedInstructions);
        NSLog(@"     Execution Time: %.3f ms", qemuStats.executionTime * 1000);
        NSLog(@"     Translation Success Rate: %.1f%%", qemuStats.translationSuccessRate * 100);
        NSLog(@"     Thumb Mode: %@", qemuStats.thumbMode ? @"YES" : @"NO");
    }
    
    // 获取热修复统计
    HotfixStats *hotfixStats = [self.hotfixManager getStatistics];
    if (hotfixStats) {
        NSLog(@"   Hotfix Statistics:");
        NSLog(@"     Total Patches: %ld", (long)hotfixStats.totalPatches);
        NSLog(@"     Applied Patches: %ld", (long)hotfixStats.appliedPatches);
        NSLog(@"     Failed Patches: %ld", (long)hotfixStats.failedPatches);
        NSLog(@"     Total Operations: %ld", (long)hotfixStats.totalOperations);
        NSLog(@"     Successful Operations: %ld", (long)hotfixStats.successfulOperations);
        NSLog(@"     Last Update: %@", hotfixStats.lastUpdateTime);
    }
    
    // 获取方法替换统计
    NSArray<MethodReplacementInfo *> *replacements = [self.methodReplacer getReplacedMethods];
    NSLog(@"   Method Replacement Statistics:");
    NSLog(@"     Total Replaced Methods: %ld", (long)replacements.count);
    
    for (MethodReplacementInfo *info in replacements) {
        NSLog(@"     - %@.%@ (%@)", info.className, info.methodName, 
              info.isActive ? @"Active" : @"Inactive");
    }
    
    NSLog(@"");
}

- (void)cleanup {
    NSLog(@"8. Cleaning up...");
    
    [self.methodReplacer cleanup];
    [self.hotfixManager cleanup];
    [self.armInterpreter cleanup];
    [self.qemuBridge cleanup];
    
    NSLog(@"   ✓ All components cleaned up");
}

@end

// 演示运行器 - 可以从应用中调用
+ (void)runHotfixDemo {
    @autoreleasepool {
        HotfixDemo *demo = [[HotfixDemo alloc] init];
        [demo runDemo];
    }
}
