/*
 * ARM Interpreter Header for iOS Hot-fix Engine
 * High-level ARM instruction execution interface
 *
 * Copyright (c) 2025
 */

#import <Foundation/Foundation.h>
#import "QEMUBridge.h"

NS_ASSUME_NONNULL_BEGIN

// ARM指令类型
typedef NS_ENUM(NSInteger, ARMInstructionType) {
    ARMInstructionTypeUnknown = 0,
    ARMInstructionTypeMOV,      // 数据移动
    ARMInstructionTypeADD,      // 加法
    ARMInstructionTypeSUB,      // 减法
    ARMInstructionTypeMUL,      // 乘法
    ARMInstructionTypeAND,      // 按位与
    ARMInstructionTypeORR,      // 按位或
    ARMInstructionTypeEOR,      // 按位异或
    ARMInstructionTypeLSL,      // 逻辑左移
    ARMInstructionTypeLSR,      // 逻辑右移
    ARMInstructionTypeASR,      // 算术右移
    ARMInstructionTypeLDR,      // 加载
    ARMInstructionTypeSTR,      // 存储
    ARMInstructionTypeB,        // 分支
    ARMInstructionTypeBL,       // 带链接的分支
    ARMInstructionTypeBX,       // 分支并交换指令集
    ARMInstructionTypeCMP,      // 比较
    ARMInstructionTypeTST,      // 测试
    ARMInstructionTypeNOP       // 无操作
};

// ARM指令信息
@interface ARMInstructionInfo : NSObject
@property (nonatomic, assign) ARMInstructionType type;
@property (nonatomic, assign) uint32_t rawInstruction;
@property (nonatomic, assign) BOOL thumbMode;
@property (nonatomic, assign) NSInteger destinationRegister;
@property (nonatomic, assign) NSInteger sourceRegister1;
@property (nonatomic, assign) NSInteger sourceRegister2;
@property (nonatomic, assign) uint32_t immediateValue;
@property (nonatomic, assign) BOOL hasImmediate;
@property (nonatomic, assign) BOOL setsFlags;
@property (nonatomic, strong) NSString *mnemonic;
@property (nonatomic, strong) NSString *operands;
@end

// ARM执行上下文
@interface ARMExecutionContext : NSObject
@property (nonatomic, strong) QEMUARMState *initialState;
@property (nonatomic, strong) QEMUARMState *finalState;
@property (nonatomic, strong) NSArray<ARMInstructionInfo *> *instructions;
@property (nonatomic, assign) NSInteger executedInstructions;
@property (nonatomic, assign) NSTimeInterval executionTime;
@property (nonatomic, strong, nullable) NSError *error;
@end

// ARM代码块
@interface ARMCodeBlock : NSObject
@property (nonatomic, strong) NSData *codeData;
@property (nonatomic, assign) BOOL thumbMode;
@property (nonatomic, assign) uint32_t baseAddress;
@property (nonatomic, strong) NSArray<ARMInstructionInfo *> *instructions;
@property (nonatomic, strong) NSString *name;
@property (nonatomic, strong, nullable) NSDictionary *metadata;

- (instancetype)initWithData:(NSData *)data thumbMode:(BOOL)thumbMode baseAddress:(uint32_t)baseAddress;
- (NSInteger)instructionCount;
- (ARMInstructionInfo * _Nullable)instructionAtIndex:(NSInteger)index;
@end

// ARM解释器主类
@interface ARMInterpreter : NSObject

// 单例
+ (instancetype)sharedInstance;

// 初始化
- (BOOL)initialize;
- (void)cleanup;
- (BOOL)isInitialized;

// 代码解析
- (ARMCodeBlock * _Nullable)parseCodeBlock:(NSData *)codeData
                                 thumbMode:(BOOL)thumbMode
                               baseAddress:(uint32_t)baseAddress
                                     error:(NSError * _Nullable * _Nullable)error;

- (ARMInstructionInfo * _Nullable)parseInstruction:(uint32_t)instruction
                                         thumbMode:(BOOL)thumbMode
                                             error:(NSError * _Nullable * _Nullable)error;

// 代码执行
- (ARMExecutionContext * _Nullable)executeCodeBlock:(ARMCodeBlock *)codeBlock
                                        withOptions:(nullable QEMUExecutionOptions *)options
                                              error:(NSError * _Nullable * _Nullable)error;

- (ARMExecutionContext * _Nullable)executeInstructions:(NSArray<NSNumber *> *)instructions
                                             thumbMode:(BOOL)thumbMode
                                           withOptions:(nullable QEMUExecutionOptions *)options
                                                 error:(NSError * _Nullable * _Nullable)error;

- (uint32_t)executeSingleInstruction:(uint32_t)instruction
                           thumbMode:(BOOL)thumbMode
                               error:(NSError * _Nullable * _Nullable)error;

// 状态管理
- (QEMUARMState *)getCurrentState;
- (void)restoreState:(QEMUARMState *)state;
- (void)resetState;

// 寄存器操作
- (uint32_t)getRegister:(QEMUARMRegister)reg;
- (void)setRegister:(QEMUARMRegister)reg value:(uint32_t)value;
- (NSDictionary<NSNumber *, NSNumber *> *)getAllRegisters;
- (void)setAllRegisters:(NSDictionary<NSNumber *, NSNumber *> *)registers;

// 内存操作
- (uint32_t)readMemory:(uint32_t)address;
- (void)writeMemory:(uint32_t)address value:(uint32_t)value;
- (NSData * _Nullable)readMemoryRange:(uint32_t)address length:(NSInteger)length;
- (BOOL)writeMemoryRange:(uint32_t)address data:(NSData *)data;

// 反汇编
- (NSString * _Nullable)disassembleInstruction:(uint32_t)instruction thumbMode:(BOOL)thumbMode;
- (NSArray<NSString *> * _Nullable)disassembleCodeBlock:(ARMCodeBlock *)codeBlock;

// 调试支持
- (void)setBreakpoint:(uint32_t)address;
- (void)removeBreakpoint:(uint32_t)address;
- (void)clearAllBreakpoints;
- (NSArray<NSNumber *> *)getBreakpoints;

// 统计信息
- (QEMUExecutionStats *)getExecutionStats;
- (void)resetStats;

// 高级执行接口
- (id _Nullable)executeARMCode:(NSData *)armCode
                     thumbMode:(BOOL)thumbMode
                    withTarget:(id)target
                      selector:(SEL)selector
                     arguments:(NSArray *)arguments
                         error:(NSError * _Nullable * _Nullable)error;

// 工具方法
+ (NSString *)instructionTypeName:(ARMInstructionType)type;
+ (NSString *)registerName:(QEMUARMRegister)reg;
+ (BOOL)isThumbInstruction:(uint32_t)instruction;
+ (NSArray<NSNumber *> *)encodeThumbMOV:(NSInteger)reg immediate:(uint32_t)immediate;
+ (NSArray<NSNumber *> *)encodeThumbADD:(NSInteger)rd rn:(NSInteger)rn rm:(NSInteger)rm;
+ (NSArray<NSNumber *> *)encodeThumbLSL:(NSInteger)rd rm:(NSInteger)rm immediate:(uint32_t)immediate;

@end

// 错误域和错误码
extern NSString * const ARMInterpreterErrorDomain;

typedef NS_ENUM(NSInteger, ARMInterpreterErrorCode) {
    ARMInterpreterErrorCodeGeneral = 2000,
    ARMInterpreterErrorCodeNotInitialized = 2001,
    ARMInterpreterErrorCodeInvalidInstruction = 2002,
    ARMInterpreterErrorCodeUnsupportedInstruction = 2003,
    ARMInterpreterErrorCodeExecutionFailed = 2004,
    ARMInterpreterErrorCodeMemoryError = 2005,
    ARMInterpreterErrorCodeInvalidAddress = 2006,
    ARMInterpreterErrorCodeInvalidRegister = 2007
};

// 通知名称
extern NSString * const ARMInterpreterDidInitializeNotification;
extern NSString * const ARMInterpreterDidCleanupNotification;
extern NSString * const ARMInterpreterExecutionDidStartNotification;
extern NSString * const ARMInterpreterExecutionDidFinishNotification;
extern NSString * const ARMInterpreterBreakpointHitNotification;
extern NSString * const ARMInterpreterErrorNotification;

NS_ASSUME_NONNULL_END
