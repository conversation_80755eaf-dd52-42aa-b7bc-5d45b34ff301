/*
 * ARM Interpreter Implementation for iOS Hot-fix Engine
 * High-level ARM instruction execution interface
 *
 * Copyright (c) 2025
 */

#import "ARMInterpreter.h"

// 导入C++头文件
extern "C" {
#include "../QEMUEngine/ARM/arm-translator.h"
#include "../QEMUEngine/Utils/qemu-utils.h"
}

#include <vector>
#include <unordered_set>

// 错误域和通知名称
NSString * const ARMInterpreterErrorDomain = @"ARMInterpreterErrorDomain";
NSString * const ARMInterpreterDidInitializeNotification = @"ARMInterpreterDidInitializeNotification";
NSString * const ARMInterpreterDidCleanupNotification = @"ARMInterpreterDidCleanupNotification";
NSString * const ARMInterpreterExecutionDidStartNotification = @"ARMInterpreterExecutionDidStartNotification";
NSString * const ARMInterpreterExecutionDidFinishNotification = @"ARMInterpreterExecutionDidFinishNotification";
NSString * const ARMInterpreterBreakpointHitNotification = @"ARMInterpreterBreakpointHitNotification";
NSString * const ARMInterpreterErrorNotification = @"ARMInterpreterErrorNotification";

@implementation ARMInstructionInfo
@end

@implementation ARMExecutionContext
@end

@implementation ARMCodeBlock

- (instancetype)initWithData:(NSData *)data thumbMode:(BOOL)thumbMode baseAddress:(uint32_t)baseAddress {
    self = [super init];
    if (self) {
        _codeData = [data copy];
        _thumbMode = thumbMode;
        _baseAddress = baseAddress;
        _name = [NSString stringWithFormat:@"CodeBlock_0x%08X", baseAddress];
        _metadata = nil;
        
        // 解析指令
        [self parseInstructions];
    }
    return self;
}

- (void)parseInstructions {
    NSMutableArray *instructions = [NSMutableArray array];
    
    const uint32_t *data = (const uint32_t *)_codeData.bytes;
    NSInteger count = _codeData.length / sizeof(uint32_t);
    
    for (NSInteger i = 0; i < count; i++) {
        ARMInstructionInfo *info = [[ARMInstructionInfo alloc] init];
        info.rawInstruction = data[i];
        info.thumbMode = _thumbMode;
        
        // 简单的指令类型识别
        [self identifyInstructionType:info];
        
        [instructions addObject:info];
    }
    
    _instructions = [instructions copy];
}

- (void)identifyInstructionType:(ARMInstructionInfo *)info {
    uint32_t insn = info.rawInstruction;
    
    if (info.thumbMode) {
        // Thumb指令识别
        if ((insn & 0xF800) == 0x2000) {
            // MOV立即数
            info.type = ARMInstructionTypeMOV;
            info.destinationRegister = (insn >> 8) & 0x7;
            info.immediateValue = insn & 0xFF;
            info.hasImmediate = YES;
            info.mnemonic = @"mov";
            info.operands = [NSString stringWithFormat:@"r%ld, #%u", 
                           (long)info.destinationRegister, info.immediateValue];
        } else if ((insn & 0xFE00) == 0x1800) {
            // ADD寄存器
            info.type = ARMInstructionTypeADD;
            info.destinationRegister = insn & 0x7;
            info.sourceRegister1 = (insn >> 3) & 0x7;
            info.sourceRegister2 = (insn >> 6) & 0x7;
            info.hasImmediate = NO;
            info.mnemonic = @"add";
            info.operands = [NSString stringWithFormat:@"r%ld, r%ld, r%ld",
                           (long)info.destinationRegister, (long)info.sourceRegister1, (long)info.sourceRegister2];
        } else if ((insn & 0xF800) == 0x0000 && ((insn >> 6) & 0x1F) != 0) {
            // LSL立即数
            info.type = ARMInstructionTypeLSL;
            info.destinationRegister = insn & 0x7;
            info.sourceRegister1 = (insn >> 3) & 0x7;
            info.immediateValue = (insn >> 6) & 0x1F;
            info.hasImmediate = YES;
            info.mnemonic = @"lsl";
            info.operands = [NSString stringWithFormat:@"r%ld, r%ld, #%u",
                           (long)info.destinationRegister, (long)info.sourceRegister1, info.immediateValue];
        } else {
            info.type = ARMInstructionTypeUnknown;
            info.mnemonic = @"unknown";
            info.operands = [NSString stringWithFormat:@"0x%04X", insn & 0xFFFF];
        }
    } else {
        // ARM指令识别（简化版）
        uint32_t opcode = (insn >> 21) & 0xF;
        
        if ((insn & 0x0C000000) == 0x00000000) {
            // 数据处理指令
            switch (opcode) {
                case 0x4: // ADD
                    info.type = ARMInstructionTypeADD;
                    info.mnemonic = @"add";
                    break;
                case 0xD: // MOV
                    info.type = ARMInstructionTypeMOV;
                    info.mnemonic = @"mov";
                    break;
                default:
                    info.type = ARMInstructionTypeUnknown;
                    info.mnemonic = @"unknown";
                    break;
            }
            
            info.destinationRegister = (insn >> 12) & 0xF;
            info.sourceRegister1 = (insn >> 16) & 0xF;
            
            if (insn & (1 << 25)) {
                // 立即数
                info.immediateValue = insn & 0xFF;
                info.hasImmediate = YES;
                info.operands = [NSString stringWithFormat:@"r%ld, #%u",
                               (long)info.destinationRegister, info.immediateValue];
            } else {
                // 寄存器
                info.sourceRegister2 = insn & 0xF;
                info.hasImmediate = NO;
                info.operands = [NSString stringWithFormat:@"r%ld, r%ld, r%ld",
                               (long)info.destinationRegister, (long)info.sourceRegister1, (long)info.sourceRegister2];
            }
        } else {
            info.type = ARMInstructionTypeUnknown;
            info.mnemonic = @"unknown";
            info.operands = [NSString stringWithFormat:@"0x%08X", insn];
        }
    }
}

- (NSInteger)instructionCount {
    return _instructions.count;
}

- (ARMInstructionInfo *)instructionAtIndex:(NSInteger)index {
    if (index >= 0 && index < _instructions.count) {
        return _instructions[index];
    }
    return nil;
}

@end

@interface ARMInterpreter () {
    BOOL _initialized;
    QEMUBridge *_qemuBridge;
    std::unordered_set<uint32_t> _breakpoints;
}

@end

@implementation ARMInterpreter

+ (instancetype)sharedInstance {
    static ARMInterpreter *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[ARMInterpreter alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _initialized = NO;
        _qemuBridge = [QEMUBridge sharedInstance];
    }
    return self;
}

- (void)dealloc {
    [self cleanup];
}

#pragma mark - 初始化

- (BOOL)initialize {
    if (_initialized) {
        return YES;
    }
    
    if (![_qemuBridge initializeEngine]) {
        return NO;
    }
    
    _initialized = YES;
    
    [[NSNotificationCenter defaultCenter] postNotificationName:ARMInterpreterDidInitializeNotification
                                                        object:self];
    
    return YES;
}

- (void)cleanup {
    if (!_initialized) {
        return;
    }
    
    [self clearAllBreakpoints];
    _initialized = NO;
    
    [[NSNotificationCenter defaultCenter] postNotificationName:ARMInterpreterDidCleanupNotification
                                                        object:self];
}

- (BOOL)isInitialized {
    return _initialized && [_qemuBridge isInitialized];
}

#pragma mark - 代码解析

- (ARMCodeBlock *)parseCodeBlock:(NSData *)codeData
                       thumbMode:(BOOL)thumbMode
                     baseAddress:(uint32_t)baseAddress
                           error:(NSError **)error {
    if (!_initialized) {
        if (error) {
            *error = [NSError errorWithDomain:ARMInterpreterErrorDomain
                                         code:ARMInterpreterErrorCodeNotInitialized
                                     userInfo:@{NSLocalizedDescriptionKey: @"ARM interpreter not initialized"}];
        }
        return nil;
    }
    
    if (!codeData || codeData.length == 0) {
        if (error) {
            *error = [NSError errorWithDomain:ARMInterpreterErrorDomain
                                         code:ARMInterpreterErrorCodeInvalidInstruction
                                     userInfo:@{NSLocalizedDescriptionKey: @"Invalid code data"}];
        }
        return nil;
    }
    
    return [[ARMCodeBlock alloc] initWithData:codeData thumbMode:thumbMode baseAddress:baseAddress];
}

- (ARMInstructionInfo *)parseInstruction:(uint32_t)instruction
                               thumbMode:(BOOL)thumbMode
                                   error:(NSError **)error {
    if (!_initialized) {
        if (error) {
            *error = [NSError errorWithDomain:ARMInterpreterErrorDomain
                                         code:ARMInterpreterErrorCodeNotInitialized
                                     userInfo:@{NSLocalizedDescriptionKey: @"ARM interpreter not initialized"}];
        }
        return nil;
    }
    
    ARMInstructionInfo *info = [[ARMInstructionInfo alloc] init];
    info.rawInstruction = instruction;
    info.thumbMode = thumbMode;
    
    // 创建临时代码块来解析单条指令
    NSData *data = [NSData dataWithBytes:&instruction length:sizeof(uint32_t)];
    ARMCodeBlock *block = [[ARMCodeBlock alloc] initWithData:data thumbMode:thumbMode baseAddress:0];
    
    if (block.instructions.count > 0) {
        ARMInstructionInfo *parsedInfo = block.instructions[0];
        info.type = parsedInfo.type;
        info.destinationRegister = parsedInfo.destinationRegister;
        info.sourceRegister1 = parsedInfo.sourceRegister1;
        info.sourceRegister2 = parsedInfo.sourceRegister2;
        info.immediateValue = parsedInfo.immediateValue;
        info.hasImmediate = parsedInfo.hasImmediate;
        info.setsFlags = parsedInfo.setsFlags;
        info.mnemonic = parsedInfo.mnemonic;
        info.operands = parsedInfo.operands;
    } else {
        info.type = ARMInstructionTypeUnknown;
        info.mnemonic = @"unknown";
        info.operands = [NSString stringWithFormat:@"0x%08X", instruction];
    }
    
    return info;
}

#pragma mark - 代码执行

- (ARMExecutionContext *)executeCodeBlock:(ARMCodeBlock *)codeBlock
                              withOptions:(QEMUExecutionOptions *)options
                                    error:(NSError **)error {
    if (!_initialized) {
        if (error) {
            *error = [NSError errorWithDomain:ARMInterpreterErrorDomain
                                         code:ARMInterpreterErrorCodeNotInitialized
                                     userInfo:@{NSLocalizedDescriptionKey: @"ARM interpreter not initialized"}];
        }
        return nil;
    }
    
    if (!codeBlock) {
        if (error) {
            *error = [NSError errorWithDomain:ARMInterpreterErrorDomain
                                         code:ARMInterpreterErrorCodeInvalidInstruction
                                     userInfo:@{NSLocalizedDescriptionKey: @"Invalid code block"}];
        }
        return nil;
    }
    
    ARMExecutionContext *context = [[ARMExecutionContext alloc] init];
    context.initialState = [_qemuBridge getCurrentState];
    context.instructions = codeBlock.instructions;
    
    [[NSNotificationCenter defaultCenter] postNotificationName:ARMInterpreterExecutionDidStartNotification
                                                        object:self];
    
    NSDate *startTime = [NSDate date];
    
    id result = nil;
    QEMUExecutionResult execResult = [_qemuBridge executeARMCode:codeBlock.codeData
                                                     withOptions:options
                                                          result:&result];
    
    NSTimeInterval executionTime = [[NSDate date] timeIntervalSinceDate:startTime];
    
    context.finalState = [_qemuBridge getCurrentState];
    context.executedInstructions = codeBlock.instructionCount;
    context.executionTime = executionTime;
    
    if (execResult != QEMUExecutionResultSuccess) {
        NSString *errorMessage = [_qemuBridge getLastError] ?: @"Unknown execution error";
        context.error = [NSError errorWithDomain:ARMInterpreterErrorDomain
                                             code:ARMInterpreterErrorCodeExecutionFailed
                                         userInfo:@{NSLocalizedDescriptionKey: errorMessage}];
        if (error) {
            *error = context.error;
        }
    }
    
    [[NSNotificationCenter defaultCenter] postNotificationName:ARMInterpreterExecutionDidFinishNotification
                                                        object:self];
    
    return context;
}

#pragma mark - 工具方法

+ (NSString *)instructionTypeName:(ARMInstructionType)type {
    switch (type) {
        case ARMInstructionTypeMOV: return @"MOV";
        case ARMInstructionTypeADD: return @"ADD";
        case ARMInstructionTypeSUB: return @"SUB";
        case ARMInstructionTypeLSL: return @"LSL";
        case ARMInstructionTypeLSR: return @"LSR";
        case ARMInstructionTypeASR: return @"ASR";
        case ARMInstructionTypeAND: return @"AND";
        case ARMInstructionTypeORR: return @"ORR";
        case ARMInstructionTypeEOR: return @"EOR";
        case ARMInstructionTypeLDR: return @"LDR";
        case ARMInstructionTypeSTR: return @"STR";
        case ARMInstructionTypeB: return @"B";
        case ARMInstructionTypeBL: return @"BL";
        case ARMInstructionTypeBX: return @"BX";
        case ARMInstructionTypeCMP: return @"CMP";
        case ARMInstructionTypeTST: return @"TST";
        case ARMInstructionTypeNOP: return @"NOP";
        default: return @"UNKNOWN";
    }
}

+ (NSString *)registerName:(QEMUARMRegister)reg {
    switch (reg) {
        case QEMUARMRegisterR0: return @"R0";
        case QEMUARMRegisterR1: return @"R1";
        case QEMUARMRegisterR2: return @"R2";
        case QEMUARMRegisterR3: return @"R3";
        case QEMUARMRegisterR4: return @"R4";
        case QEMUARMRegisterR5: return @"R5";
        case QEMUARMRegisterR6: return @"R6";
        case QEMUARMRegisterR7: return @"R7";
        case QEMUARMRegisterR8: return @"R8";
        case QEMUARMRegisterR9: return @"R9";
        case QEMUARMRegisterR10: return @"R10";
        case QEMUARMRegisterR11: return @"R11";
        case QEMUARMRegisterR12: return @"R12";
        case QEMUARMRegisterR13: return @"SP";
        case QEMUARMRegisterR14: return @"LR";
        case QEMUARMRegisterR15: return @"PC";
        default: return @"UNKNOWN";
    }
}

+ (BOOL)isThumbInstruction:(uint32_t)instruction {
    // 简单的Thumb指令检测
    return (instruction & 0xFFFF0000) == 0;
}

+ (NSArray<NSNumber *> *)encodeThumbMOV:(NSInteger)reg immediate:(uint32_t)immediate {
    if (reg < 0 || reg > 7 || immediate > 255) {
        return nil;
    }
    
    uint16_t instruction = 0x2000 | ((reg & 0x7) << 8) | (immediate & 0xFF);
    return @[@(instruction)];
}

+ (NSArray<NSNumber *> *)encodeThumbADD:(NSInteger)rd rn:(NSInteger)rn rm:(NSInteger)rm {
    if (rd < 0 || rd > 7 || rn < 0 || rn > 7 || rm < 0 || rm > 7) {
        return nil;
    }
    
    uint16_t instruction = 0x1800 | ((rm & 0x7) << 6) | ((rn & 0x7) << 3) | (rd & 0x7);
    return @[@(instruction)];
}

+ (NSArray<NSNumber *> *)encodeThumbLSL:(NSInteger)rd rm:(NSInteger)rm immediate:(uint32_t)immediate {
    if (rd < 0 || rd > 7 || rm < 0 || rm > 7 || immediate > 31 || immediate == 0) {
        return nil;
    }
    
    uint16_t instruction = 0x0000 | ((immediate & 0x1F) << 6) | ((rm & 0x7) << 3) | (rd & 0x7);
    return @[@(instruction)];
}

// ARM代码执行方法
- (id)executeARMCode:(NSData *)armCode
           thumbMode:(BOOL)thumbMode
          withTarget:(id)target
            selector:(SEL)selector
           arguments:(NSArray *)arguments
               error:(NSError **)error {

    if (!_initialized) {
        if (error) {
            *error = [NSError errorWithDomain:ARMInterpreterErrorDomain
                                         code:ARMInterpreterErrorCodeNotInitialized
                                     userInfo:@{NSLocalizedDescriptionKey: @"ARM interpreter not initialized"}];
        }
        return nil;
    }

    QEMUExecutionOptions *options = [[QEMUExecutionOptions alloc] init];
    options.thumbMode = thumbMode;

    id result = nil;
    QEMUExecutionResult execResult = [_qemuBridge executeARMCode:armCode
                                                     withOptions:options
                                                          result:&result];

    if (execResult != QEMUExecutionResultSuccess) {
        if (error) {
            NSString *errorMessage = [_qemuBridge getLastError] ?: @"ARM code execution failed";
            *error = [NSError errorWithDomain:ARMInterpreterErrorDomain
                                         code:ARMInterpreterErrorCodeExecutionFailed
                                     userInfo:@{NSLocalizedDescriptionKey: errorMessage}];
        }
        return nil;
    }

    return result;
}

#pragma mark - 指令执行方法

- (ARMExecutionContext *)executeInstructions:(NSArray<NSNumber *> *)instructions
                                    thumbMode:(BOOL)thumbMode
                                  withOptions:(QEMUExecutionOptions *)options
                                        error:(NSError **)error {
    if (!_initialized) {
        if (error) {
            *error = [NSError errorWithDomain:ARMInterpreterErrorDomain
                                         code:ARMInterpreterErrorCodeNotInitialized
                                     userInfo:@{NSLocalizedDescriptionKey: @"ARM interpreter not initialized"}];
        }
        return nil;
    }

    if (!instructions || instructions.count == 0) {
        if (error) {
            *error = [NSError errorWithDomain:ARMInterpreterErrorDomain
                                         code:ARMInterpreterErrorCodeInvalidInstruction
                                     userInfo:@{NSLocalizedDescriptionKey: @"Invalid instructions array"}];
        }
        return nil;
    }

    // 将NSArray转换为NSData
    NSMutableData *codeData = [NSMutableData data];
    for (NSNumber *instruction in instructions) {
        uint32_t insn = instruction.unsignedIntValue;
        [codeData appendBytes:&insn length:sizeof(uint32_t)];
    }

    // 创建代码块并执行
    ARMCodeBlock *codeBlock = [[ARMCodeBlock alloc] initWithData:codeData
                                                       thumbMode:thumbMode
                                                     baseAddress:0];

    return [self executeCodeBlock:codeBlock withOptions:options error:error];
}

- (uint32_t)executeSingleInstruction:(uint32_t)instruction
                           thumbMode:(BOOL)thumbMode
                               error:(NSError **)error {
    if (!_initialized) {
        if (error) {
            *error = [NSError errorWithDomain:ARMInterpreterErrorDomain
                                         code:ARMInterpreterErrorCodeNotInitialized
                                     userInfo:@{NSLocalizedDescriptionKey: @"ARM interpreter not initialized"}];
        }
        return 0;
    }

    // 创建单指令数组
    NSArray<NSNumber *> *instructions = @[@(instruction)];

    // 执行指令
    ARMExecutionContext *context = [self executeInstructions:instructions
                                                    thumbMode:thumbMode
                                                  withOptions:nil
                                                        error:error];

    if (context && !context.error) {
        // 返回PC寄存器的值作为结果
        return [self getRegister:QEMUARMRegisterR15];
    }

    return 0;
}

#pragma mark - 简化的方法实现（委托给QEMUBridge）

- (uint32_t)getRegister:(QEMUARMRegister)reg {
    return [_qemuBridge getRegisterValue:reg];
}

- (void)setRegister:(QEMUARMRegister)reg value:(uint32_t)value {
    [_qemuBridge setRegisterValue:reg value:value];
}

- (QEMUARMState *)getCurrentState {
    return [_qemuBridge getCurrentState];
}

- (void)restoreState:(QEMUARMState *)state {
    [_qemuBridge restoreState:state];
}

- (void)resetState {
    if (!_initialized) {
        return;
    }

    // 重置所有寄存器为0
    for (int i = QEMUARMRegisterR0; i <= QEMUARMRegisterR15; i++) {
        [_qemuBridge setRegisterValue:(QEMUARMRegister)i value:0];
    }

    // 重置统计信息
    [_qemuBridge resetStats];
}

- (NSDictionary<NSNumber *, NSNumber *> *)getAllRegisters {
    if (!_initialized) {
        return @{};
    }

    NSMutableDictionary *registers = [NSMutableDictionary dictionary];

    for (int i = QEMUARMRegisterR0; i <= QEMUARMRegisterR15; i++) {
        QEMUARMRegister reg = (QEMUARMRegister)i;
        uint32_t value = [_qemuBridge getRegisterValue:reg];
        registers[@(reg)] = @(value);
    }

    return [registers copy];
}

- (void)setAllRegisters:(NSDictionary<NSNumber *, NSNumber *> *)registers {
    if (!_initialized || !registers) {
        return;
    }

    for (NSNumber *regNumber in registers) {
        QEMUARMRegister reg = (QEMUARMRegister)regNumber.intValue;
        uint32_t value = registers[regNumber].unsignedIntValue;

        // 验证寄存器范围
        if (reg >= QEMUARMRegisterR0 && reg <= QEMUARMRegisterR15) {
            [_qemuBridge setRegisterValue:reg value:value];
        }
    }
}

#pragma mark - 内存操作方法

- (uint32_t)readMemory:(uint32_t)address {
    if (!_initialized) {
        return 0;
    }

    // 委托给QEMUBridge处理内存读取
    // 注意：这里需要QEMUBridge实现相应的内存操作方法
    // 暂时返回0作为占位符
    return 0;
}

- (void)writeMemory:(uint32_t)address value:(uint32_t)value {
    if (!_initialized) {
        return;
    }

    // 委托给QEMUBridge处理内存写入
    // 注意：这里需要QEMUBridge实现相应的内存操作方法
}

- (NSData *)readMemoryRange:(uint32_t)address length:(NSInteger)length {
    if (!_initialized || length <= 0) {
        return nil;
    }

    // 委托给QEMUBridge处理内存范围读取
    // 暂时返回空数据作为占位符
    return [NSData data];
}

- (BOOL)writeMemoryRange:(uint32_t)address data:(NSData *)data {
    if (!_initialized || !data || data.length == 0) {
        return NO;
    }

    // 委托给QEMUBridge处理内存范围写入
    // 暂时返回YES作为占位符
    return YES;
}

#pragma mark - 反汇编方法

- (NSString *)disassembleInstruction:(uint32_t)instruction thumbMode:(BOOL)thumbMode {
    if (!_initialized) {
        return nil;
    }

    // 解析指令
    ARMInstructionInfo *info = [self parseInstruction:instruction thumbMode:thumbMode error:nil];
    if (!info) {
        return [NSString stringWithFormat:@"unknown 0x%08X", instruction];
    }

    // 返回助记符和操作数
    if (info.operands && info.operands.length > 0) {
        return [NSString stringWithFormat:@"%@ %@", info.mnemonic, info.operands];
    } else {
        return info.mnemonic;
    }
}

- (NSArray<NSString *> *)disassembleCodeBlock:(ARMCodeBlock *)codeBlock {
    if (!_initialized || !codeBlock) {
        return nil;
    }

    NSMutableArray *disassembly = [NSMutableArray array];

    for (NSInteger i = 0; i < codeBlock.instructionCount; i++) {
        ARMInstructionInfo *info = [codeBlock instructionAtIndex:i];
        if (info) {
            NSString *line;
            if (info.operands && info.operands.length > 0) {
                line = [NSString stringWithFormat:@"0x%08X: %@ %@",
                       codeBlock.baseAddress + (uint32_t)(i * 4), info.mnemonic, info.operands];
            } else {
                line = [NSString stringWithFormat:@"0x%08X: %@",
                       codeBlock.baseAddress + (uint32_t)(i * 4), info.mnemonic];
            }
            [disassembly addObject:line];
        } else {
            NSString *line = [NSString stringWithFormat:@"0x%08X: unknown",
                             codeBlock.baseAddress + (uint32_t)(i * 4)];
            [disassembly addObject:line];
        }
    }

    return [disassembly copy];
}

- (QEMUExecutionStats *)getExecutionStats {
    return [_qemuBridge getExecutionStats];
}

- (void)resetStats {
    [_qemuBridge resetStats];
}

#pragma mark - 断点管理（简化实现）

- (void)setBreakpoint:(uint32_t)address {
    _breakpoints.insert(address);
}

- (void)removeBreakpoint:(uint32_t)address {
    _breakpoints.erase(address);
}

- (void)clearAllBreakpoints {
    _breakpoints.clear();
}

- (NSArray<NSNumber *> *)getBreakpoints {
    NSMutableArray *result = [NSMutableArray array];
    for (uint32_t addr : _breakpoints) {
        [result addObject:@(addr)];
    }
    return [result copy];
}



#pragma mark - 私有辅助方法

- (NSError *)errorWithCode:(ARMInterpreterErrorCode)code description:(NSString *)description {
    return [NSError errorWithDomain:ARMInterpreterErrorDomain
                               code:code
                           userInfo:@{NSLocalizedDescriptionKey: description}];
}

@end
