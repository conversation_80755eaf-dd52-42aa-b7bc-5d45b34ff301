/*
 * Method Replacer Implementation for iOS Hot-fix Engine
 * Handles runtime method replacement using Objective-C runtime
 *
 * Copyright (c) 2025
 */

#import "MethodReplacer.h"
#import "ARMInterpreter.h"
#import <objc/runtime.h>
#import <objc/message.h>

// 错误域和通知名称
NSString * const MethodReplacerErrorDomain = @"MethodReplacerErrorDomain";
NSString * const MethodReplacerDidInitializeNotification = @"MethodReplacerDidInitializeNotification";
NSString * const MethodReplacerDidCleanupNotification = @"MethodReplacerDidCleanupNotification";
NSString * const MethodReplacerMethodDidReplaceNotification = @"MethodReplacerMethodDidReplaceNotification";
NSString * const MethodReplacerMethodDidRestoreNotification = @"MethodReplacerMethodDidRestoreNotification";
NSString * const MethodReplacerMethodDidExecuteNotification = @"MethodReplacerMethodDidExecuteNotification";
NSString * const MethodReplacerErrorNotification = @"MethodReplacerErrorNotification";

// 通知用户信息键
NSString * const MethodReplacerClassNameKey = @"MethodReplacerClassNameKey";
NSString * const MethodReplacerMethodNameKey = @"MethodReplacerMethodNameKey";
NSString * const MethodReplacerReplacementInfoKey = @"MethodReplacerReplacementInfoKey";
NSString * const MethodReplacerExecutionContextKey = @"MethodReplacerExecutionContextKey";
NSString * const MethodReplacerErrorKey = @"MethodReplacerErrorKey";

@implementation MethodReplacementInfo
@end

@implementation MethodExecutionContext
@end

@interface MethodReplacer () {
    BOOL _initialized;
    BOOL _debugMode;
    NSMutableDictionary<NSString *, MethodReplacementInfo *> *_replacements;
    NSMutableDictionary<NSString *, NSNumber *> *_executionCounts;
    NSMutableDictionary<NSString *, NSNumber *> *_executionTimes;
    ARMInterpreter *_armInterpreter;
    id<MethodInterceptor> _interceptor;
    NSArray<NSString *> *_unsafeClasses;
    NSArray<NSString *> *_unsafeMethods;
}

@end

@implementation MethodReplacer

+ (instancetype)sharedInstance {
    static MethodReplacer *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[MethodReplacer alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _initialized = NO;
        _debugMode = NO;
        _replacements = [NSMutableDictionary dictionary];
        _executionCounts = [NSMutableDictionary dictionary];
        _executionTimes = [NSMutableDictionary dictionary];
        _armInterpreter = [ARMInterpreter sharedInstance];
        _interceptor = nil;
        
        // 定义不安全的类和方法
        _unsafeClasses = @[
            @"NSObject", @"NSString", @"NSArray", @"NSDictionary", @"NSSet",
            @"NSNumber", @"NSData", @"NSDate", @"NSURL", @"NSBundle",
            @"UIApplication", @"UIWindow", @"UIViewController", @"UIView",
            @"CALayer", @"NSThread", @"NSRunLoop", @"NSNotificationCenter"
        ];
        
        _unsafeMethods = @[
            @"init", @"dealloc", @"retain", @"release", @"autorelease",
            @"retainCount", @"class", @"superclass", @"isKindOfClass:",
            @"isMemberOfClass:", @"respondsToSelector:", @"performSelector:",
            @"methodForSelector:", @"instanceMethodForSelector:"
        ];
    }
    return self;
}

- (void)dealloc {
    [self cleanup];
}

#pragma mark - 初始化

- (BOOL)initialize {
    if (_initialized) {
        return YES;
    }
    
    if (![_armInterpreter initialize]) {
        return NO;
    }
    
    _initialized = YES;
    
    [[NSNotificationCenter defaultCenter] postNotificationName:MethodReplacerDidInitializeNotification
                                                        object:self];
    
    return YES;
}

- (void)cleanup {
    if (!_initialized) {
        return;
    }
    
    // 恢复所有被替换的方法
    NSError *error = nil;
    [self restoreAllMethods:&error];
    if (error && _debugMode) {
        NSLog(@"Warning: Failed to restore some methods during cleanup: %@", error.localizedDescription);
    }
    
    [_replacements removeAllObjects];
    [_executionCounts removeAllObjects];
    [_executionTimes removeAllObjects];
    
    _initialized = NO;
    
    [[NSNotificationCenter defaultCenter] postNotificationName:MethodReplacerDidCleanupNotification
                                                        object:self];
}

- (BOOL)isInitialized {
    return _initialized;
}

#pragma mark - 方法替换

- (BOOL)replaceMethod:(SEL)selector
              inClass:(Class)targetClass
         withARMCode:(NSData *)armCode
           thumbMode:(BOOL)thumbMode
               error:(NSError **)error {
    return [self replaceInstanceMethod:selector
                               inClass:targetClass
                          withARMCode:armCode
                            thumbMode:thumbMode
                                error:error];
}

- (BOOL)replaceInstanceMethod:(SEL)selector
                      inClass:(Class)targetClass
                 withARMCode:(NSData *)armCode
                   thumbMode:(BOOL)thumbMode
                       error:(NSError **)error {
    if (!_initialized) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeNotInitialized
                             description:@"Method replacer not initialized"];
        }
        return NO;
    }
    
    if (!selector || !targetClass || !armCode) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeInvalidClass
                             description:@"Invalid parameters"];
        }
        return NO;
    }
    
    // 安全检查
    if (![self isSafeToReplace:selector inClass:targetClass]) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeUnsafeOperation
                             description:@"Unsafe to replace this method"];
        }
        return NO;
    }
    
    // 检查方法是否存在
    if (![self isMethodExists:selector inClass:targetClass]) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeMethodNotFound
                             description:@"Method not found"];
        }
        return NO;
    }
    
    NSString *key = [self keyForSelector:selector inClass:targetClass];
    
    // 检查是否已经被替换
    if (_replacements[key]) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeReplacementFailed
                             description:@"Method already replaced"];
        }
        return NO;
    }
    
    @try {
        // 获取原始方法
        Method originalMethod = class_getInstanceMethod(targetClass, selector);
        if (!originalMethod) {
            if (error) {
                *error = [self errorWithCode:MethodReplacerErrorCodeMethodNotFound
                             description:@"Failed to get original method"];
            }
            return NO;
        }
        
        // 保存原始实现
        IMP originalIMP = method_getImplementation(originalMethod);
        const char *typeEncoding = method_getTypeEncoding(originalMethod);
        
        // 创建新的实现
        IMP newIMP = [self createARMImplementation:armCode
                                         thumbMode:thumbMode
                                          selector:selector
                                       targetClass:targetClass
                                      typeEncoding:typeEncoding];
        
        if (!newIMP) {
            if (error) {
                *error = [self errorWithCode:MethodReplacerErrorCodeReplacementFailed
                             description:@"Failed to create ARM implementation"];
            }
            return NO;
        }
        
        // 替换方法实现
        method_setImplementation(originalMethod, newIMP);
        
        // 记录替换信息
        MethodReplacementInfo *info = [[MethodReplacementInfo alloc] init];
        info.className = NSStringFromClass(targetClass);
        info.methodName = NSStringFromSelector(selector);
        info.isInstanceMethod = YES;
        info.type = MethodReplacementTypeReplace;
        info.originalImplementation = [NSData dataWithBytes:&originalIMP length:sizeof(IMP)];
        info.armCode = armCode;
        info.thumbMode = thumbMode;
        info.replacedDate = [NSDate date];
        info.isActive = YES;
        
        _replacements[key] = info;
        
        // 发送通知
        [[NSNotificationCenter defaultCenter] postNotificationName:MethodReplacerMethodDidReplaceNotification
                                                            object:self
                                                          userInfo:@{
                                                              MethodReplacerClassNameKey: info.className,
                                                              MethodReplacerMethodNameKey: info.methodName,
                                                              MethodReplacerReplacementInfoKey: info
                                                          }];
        
        return YES;
        
    } @catch (NSException *exception) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeReplacementFailed
                             description:[NSString stringWithFormat:@"Exception during replacement: %@", exception.reason]];
        }
        return NO;
    }
}

- (BOOL)replaceClassMethod:(SEL)selector
                   inClass:(Class)targetClass
              withARMCode:(NSData *)armCode
                thumbMode:(BOOL)thumbMode
                    error:(NSError **)error {
    // 类方法替换类似于实例方法，但操作的是元类
    Class metaClass = object_getClass(targetClass);
    return [self replaceInstanceMethod:selector
                               inClass:metaClass
                          withARMCode:armCode
                            thumbMode:thumbMode
                                error:error];
}

#pragma mark - 方法交换

- (BOOL)swizzleMethod:(SEL)originalSelector
           withMethod:(SEL)swizzledSelector
              inClass:(Class)targetClass
                error:(NSError **)error {
    return [self swizzleInstanceMethod:originalSelector
                            withMethod:swizzledSelector
                               inClass:targetClass
                                 error:error];
}

- (BOOL)swizzleInstanceMethod:(SEL)originalSelector
                   withMethod:(SEL)swizzledSelector
                      inClass:(Class)targetClass
                        error:(NSError **)error {
    if (!_initialized) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeNotInitialized
                             description:@"Method replacer not initialized"];
        }
        return NO;
    }

    if (!originalSelector || !swizzledSelector || !targetClass) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeInvalidClass
                             description:@"Invalid parameters"];
        }
        return NO;
    }

    // 安全检查
    if (![self isSafeToReplace:originalSelector inClass:targetClass] ||
        ![self isSafeToReplace:swizzledSelector inClass:targetClass]) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeUnsafeOperation
                             description:@"Unsafe to swizzle these methods"];
        }
        return NO;
    }

    @try {
        Method originalMethod = class_getInstanceMethod(targetClass, originalSelector);
        Method swizzledMethod = class_getInstanceMethod(targetClass, swizzledSelector);

        if (!originalMethod || !swizzledMethod) {
            if (error) {
                *error = [self errorWithCode:MethodReplacerErrorCodeMethodNotFound
                             description:@"One or both methods not found"];
            }
            return NO;
        }

        // 交换方法实现
        method_exchangeImplementations(originalMethod, swizzledMethod);

        // 记录交换信息
        NSString *key = [self keyForSelector:originalSelector inClass:targetClass];
        MethodReplacementInfo *info = [[MethodReplacementInfo alloc] init];
        info.className = NSStringFromClass(targetClass);
        info.methodName = NSStringFromSelector(originalSelector);
        info.isInstanceMethod = YES;
        info.type = MethodReplacementTypeSwizzle;
        info.replacedDate = [NSDate date];
        info.isActive = YES;

        _replacements[key] = info;

        // 发送通知
        [[NSNotificationCenter defaultCenter] postNotificationName:MethodReplacerMethodDidReplaceNotification
                                                            object:self
                                                          userInfo:@{
                                                              MethodReplacerClassNameKey: info.className,
                                                              MethodReplacerMethodNameKey: info.methodName,
                                                              MethodReplacerReplacementInfoKey: info
                                                          }];

        return YES;

    } @catch (NSException *exception) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeReplacementFailed
                             description:[NSString stringWithFormat:@"Exception during swizzle: %@", exception.reason]];
        }
        return NO;
    }
}

- (BOOL)swizzleClassMethod:(SEL)originalSelector
            withMethod:(SEL)swizzledSelector
               inClass:(Class)targetClass
                 error:(NSError **)error {
    // 类方法交换操作元类
    Class metaClass = object_getClass(targetClass);
    return [self swizzleInstanceMethod:originalSelector
                            withMethod:swizzledSelector
                               inClass:metaClass
                                 error:error];
}

#pragma mark - 方法钩子

- (BOOL)hookMethod:(SEL)selector
           inClass:(Class)targetClass
      withARMCode:(NSData *)armCode
        thumbMode:(BOOL)thumbMode
         position:(NSString *)position
            error:(NSError **)error {
    if (!_initialized) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeNotInitialized
                             description:@"Method replacer not initialized"];
        }
        return NO;
    }

    if (!selector || !targetClass || !armCode || !position) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeInvalidClass
                             description:@"Invalid parameters"];
        }
        return NO;
    }

    // 验证position参数
    NSArray *validPositions = @[@"before", @"after", @"around"];
    if (![validPositions containsObject:position]) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeInvalidClass
                             description:@"Invalid hook position. Must be 'before', 'after', or 'around'"];
        }
        return NO;
    }

    // 安全检查
    if (![self isSafeToReplace:selector inClass:targetClass]) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeUnsafeOperation
                             description:@"Unsafe to hook this method"];
        }
        return NO;
    }

    @try {
        // 获取原始方法
        Method originalMethod = class_getInstanceMethod(targetClass, selector);
        if (!originalMethod) {
            if (error) {
                *error = [self errorWithCode:MethodReplacerErrorCodeMethodNotFound
                             description:@"Method not found"];
            }
            return NO;
        }

        // 保存原始实现
        IMP originalIMP = method_getImplementation(originalMethod);
        const char *typeEncoding = method_getTypeEncoding(originalMethod);

        // 创建钩子实现
        IMP hookIMP = [self createHookImplementation:armCode
                                           thumbMode:thumbMode
                                            position:position
                                         originalIMP:originalIMP
                                            selector:selector
                                         targetClass:targetClass
                                        typeEncoding:typeEncoding];

        if (!hookIMP) {
            if (error) {
                *error = [self errorWithCode:MethodReplacerErrorCodeReplacementFailed
                             description:@"Failed to create hook implementation"];
            }
            return NO;
        }

        // 替换方法实现
        method_setImplementation(originalMethod, hookIMP);

        // 记录钩子信息
        NSString *key = [self keyForSelector:selector inClass:targetClass];
        MethodReplacementInfo *info = [[MethodReplacementInfo alloc] init];
        info.className = NSStringFromClass(targetClass);
        info.methodName = NSStringFromSelector(selector);
        info.isInstanceMethod = YES;
        info.type = MethodReplacementTypeHook;
        info.originalImplementation = [NSData dataWithBytes:&originalIMP length:sizeof(IMP)];
        info.armCode = armCode;
        info.thumbMode = thumbMode;
        info.replacedDate = [NSDate date];
        info.isActive = YES;

        _replacements[key] = info;

        return YES;

    } @catch (NSException *exception) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeReplacementFailed
                             description:[NSString stringWithFormat:@"Exception during hook: %@", exception.reason]];
        }
        return NO;
    }
}

#pragma mark - 方法恢复

- (BOOL)restoreMethod:(SEL)selector
              inClass:(Class)targetClass
                error:(NSError **)error {
    if (!_initialized) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeNotInitialized
                             description:@"Method replacer not initialized"];
        }
        return NO;
    }
    
    NSString *key = [self keyForSelector:selector inClass:targetClass];
    MethodReplacementInfo *info = _replacements[key];
    
    if (!info || !info.isActive) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeRestoreFailed
                             description:@"Method not replaced or already restored"];
        }
        return NO;
    }
    
    @try {
        // 获取方法
        Method method = class_getInstanceMethod(targetClass, selector);
        if (!method) {
            if (error) {
                *error = [self errorWithCode:MethodReplacerErrorCodeMethodNotFound
                             description:@"Method not found"];
            }
            return NO;
        }
        
        // 恢复原始实现
        IMP originalIMP;
        [info.originalImplementation getBytes:&originalIMP length:sizeof(IMP)];
        method_setImplementation(method, originalIMP);
        
        // 更新状态
        info.isActive = NO;
        [_replacements removeObjectForKey:key];
        
        // 发送通知
        [[NSNotificationCenter defaultCenter] postNotificationName:MethodReplacerMethodDidRestoreNotification
                                                            object:self
                                                          userInfo:@{
                                                              MethodReplacerClassNameKey: info.className,
                                                              MethodReplacerMethodNameKey: info.methodName,
                                                              MethodReplacerReplacementInfoKey: info
                                                          }];
        
        return YES;
        
    } @catch (NSException *exception) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeRestoreFailed
                             description:[NSString stringWithFormat:@"Exception during restore: %@", exception.reason]];
        }
        return NO;
    }
}

- (BOOL)restoreAllMethods:(NSError **)error {
    NSArray *keys = [_replacements.allKeys copy];
    BOOL allSuccess = YES;
    NSMutableString *errorMessages = [NSMutableString string];

    for (NSString *key in keys) {
        MethodReplacementInfo *info = _replacements[key];
        Class targetClass = NSClassFromString(info.className);
        SEL selector = NSSelectorFromString(info.methodName);

        NSError *restoreError = nil;
        if (![self restoreMethod:selector inClass:targetClass error:&restoreError]) {
            allSuccess = NO;
            [errorMessages appendFormat:@"Failed to restore %@.%@: %@\n",
             info.className, info.methodName, restoreError.localizedDescription];
        }
    }

    if (!allSuccess && error) {
        *error = [self errorWithCode:MethodReplacerErrorCodeRestoreFailed
                         description:errorMessages];
    }

    return allSuccess;
}

- (BOOL)restoreAllMethodsInClass:(Class)targetClass error:(NSError **)error {
    if (!targetClass) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeInvalidClass
                             description:@"Invalid class"];
        }
        return NO;
    }

    NSString *className = NSStringFromClass(targetClass);
    NSArray *keys = [_replacements.allKeys copy];
    BOOL allSuccess = YES;
    NSMutableString *errorMessages = [NSMutableString string];

    for (NSString *key in keys) {
        MethodReplacementInfo *info = _replacements[key];
        if ([info.className isEqualToString:className]) {
            SEL selector = NSSelectorFromString(info.methodName);

            NSError *restoreError = nil;
            if (![self restoreMethod:selector inClass:targetClass error:&restoreError]) {
                allSuccess = NO;
                [errorMessages appendFormat:@"Failed to restore %@.%@: %@\n",
                 info.className, info.methodName, restoreError.localizedDescription];
            }
        }
    }

    if (!allSuccess && error) {
        *error = [self errorWithCode:MethodReplacerErrorCodeRestoreFailed
                         description:errorMessages];
    }

    return allSuccess;
}

#pragma mark - ARM实现创建

- (IMP)createARMImplementation:(NSData *)armCode
                     thumbMode:(BOOL)thumbMode
                      selector:(SEL)selector
                   targetClass:(Class)targetClass
                  typeEncoding:(const char *)typeEncoding {
    // 创建一个C函数作为新的方法实现
    // 这个函数会调用ARM解释器执行ARM代码
    
    // 注意：这是一个简化的实现，实际应用中需要根据方法签名动态生成
    IMP newIMP = imp_implementationWithBlock(^id(id self, ...) {
        @try {
            // 准备参数
            va_list args;
            va_start(args, self);
            
            NSMutableArray *arguments = [NSMutableArray arrayWithObject:self];
            
            // 这里需要根据方法签名解析参数
            // 简化处理，假设最多支持4个参数
            for (int i = 0; i < 4; i++) {
                id arg = va_arg(args, id);
                if (arg) {
                    [arguments addObject:arg];
                } else {
                    break;
                }
            }
            
            va_end(args);
            
            // 执行ARM代码
            NSError *execError = nil;
            id result = [_armInterpreter executeARMCode:armCode
                                              thumbMode:thumbMode
                                             withTarget:self
                                               selector:selector
                                              arguments:arguments
                                                  error:&execError];
            
            if (execError) {
                NSLog(@"ARM execution error: %@", execError.localizedDescription);
                return nil;
            }
            
            // 更新统计信息
            NSString *key = [self keyForSelector:selector inClass:targetClass];
            [self updateExecutionStats:key];
            
            return result;
            
        } @catch (NSException *exception) {
            NSLog(@"Exception in ARM implementation: %@", exception.reason);
            return nil;
        }
    });
    
    return newIMP;
}

#pragma mark - 私有方法

- (NSString *)keyForSelector:(SEL)selector inClass:(Class)targetClass {
    return [NSString stringWithFormat:@"%@.%@", NSStringFromClass(targetClass), NSStringFromSelector(selector)];
}

- (NSError *)errorWithCode:(MethodReplacerErrorCode)code description:(NSString *)description {
    return [NSError errorWithDomain:MethodReplacerErrorDomain
                               code:code
                           userInfo:@{NSLocalizedDescriptionKey: description}];
}

- (void)updateExecutionStats:(NSString *)key {
    // 更新执行次数
    NSNumber *count = _executionCounts[key] ?: @0;
    _executionCounts[key] = @(count.integerValue + 1);
    
    // 更新执行时间（简化处理）
    NSNumber *time = _executionTimes[key] ?: @0;
    _executionTimes[key] = @(time.doubleValue + 0.001); // 假设每次执行1ms
}

#pragma mark - 查询方法

- (NSArray<MethodReplacementInfo *> *)getReplacedMethods {
    return [_replacements.allValues copy];
}

- (NSArray<MethodReplacementInfo *> *)getReplacedMethodsInClass:(Class)targetClass {
    if (!targetClass) {
        return @[];
    }

    NSString *className = NSStringFromClass(targetClass);
    NSMutableArray *result = [NSMutableArray array];

    for (MethodReplacementInfo *info in _replacements.allValues) {
        if ([info.className isEqualToString:className] && info.isActive) {
            [result addObject:info];
        }
    }

    return [result copy];
}

- (MethodReplacementInfo *)getReplacementInfo:(SEL)selector inClass:(Class)targetClass {
    NSString *key = [self keyForSelector:selector inClass:targetClass];
    return _replacements[key];
}

- (BOOL)isMethodReplaced:(SEL)selector inClass:(Class)targetClass {
    NSString *key = [self keyForSelector:selector inClass:targetClass];
    MethodReplacementInfo *info = _replacements[key];
    return info && info.isActive;
}

#pragma mark - 方法验证

- (BOOL)isMethodExists:(SEL)selector inClass:(Class)targetClass {
    return class_getInstanceMethod(targetClass, selector) != NULL;
}

- (BOOL)isInstanceMethod:(SEL)selector inClass:(Class)targetClass {
    return class_getInstanceMethod(targetClass, selector) != NULL;
}

- (BOOL)isClassMethod:(SEL)selector inClass:(Class)targetClass {
    return class_getClassMethod(targetClass, selector) != NULL;
}

- (NSString *)getMethodTypeEncoding:(SEL)selector inClass:(Class)targetClass {
    Method method = class_getInstanceMethod(targetClass, selector);
    if (!method) {
        method = class_getClassMethod(targetClass, selector);
    }

    if (method) {
        const char *encoding = method_getTypeEncoding(method);
        return encoding ? [NSString stringWithUTF8String:encoding] : nil;
    }

    return nil;
}

#pragma mark - ARM代码执行

- (id)executeARMCode:(NSData *)armCode
           thumbMode:(BOOL)thumbMode
          withTarget:(id)target
            selector:(SEL)selector
           arguments:(NSArray *)arguments
               error:(NSError **)error {
    if (!_initialized) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeNotInitialized
                             description:@"Method replacer not initialized"];
        }
        return nil;
    }

    if (!armCode || !target || !selector) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeInvalidClass
                             description:@"Invalid parameters"];
        }
        return nil;
    }

    @try {
        // 委托给ARM解释器执行
        return [_armInterpreter executeARMCode:armCode
                                     thumbMode:thumbMode
                                    withTarget:target
                                      selector:selector
                                     arguments:arguments
                                         error:error];
    } @catch (NSException *exception) {
        if (error) {
            *error = [self errorWithCode:MethodReplacerErrorCodeExecutionFailed
                             description:[NSString stringWithFormat:@"ARM execution exception: %@", exception.reason]];
        }
        return nil;
    }
}

#pragma mark - 方法调用拦截

- (void)setMethodInterceptor:(id<MethodInterceptor>)interceptor {
    _interceptor = interceptor;
}

- (id<MethodInterceptor>)getMethodInterceptor {
    return _interceptor;
}

#pragma mark - 性能监控

- (NSTimeInterval)getAverageExecutionTime:(SEL)selector inClass:(Class)targetClass {
    NSString *key = [self keyForSelector:selector inClass:targetClass];
    NSNumber *totalTime = _executionTimes[key];
    NSNumber *count = _executionCounts[key];

    if (!totalTime || !count || count.integerValue == 0) {
        return 0.0;
    }

    return totalTime.doubleValue / count.integerValue;
}

- (NSInteger)getExecutionCount:(SEL)selector inClass:(Class)targetClass {
    NSString *key = [self keyForSelector:selector inClass:targetClass];
    NSNumber *count = _executionCounts[key];
    return count ? count.integerValue : 0;
}

- (void)resetPerformanceStats {
    [_executionCounts removeAllObjects];
    [_executionTimes removeAllObjects];
}

#pragma mark - 调试支持扩展

- (void)dumpMethodInfo:(SEL)selector inClass:(Class)targetClass {
    if (!_debugMode) {
        return;
    }

    NSString *className = NSStringFromClass(targetClass);
    NSString *methodName = NSStringFromSelector(selector);
    NSString *key = [self keyForSelector:selector inClass:targetClass];

    NSLog(@"=== Method Info Dump ===");
    NSLog(@"Class: %@", className);
    NSLog(@"Method: %@", methodName);
    NSLog(@"Key: %@", key);

    // 检查方法是否存在
    BOOL isInstance = [self isInstanceMethod:selector inClass:targetClass];
    BOOL isClass = [self isClassMethod:selector inClass:targetClass];
    NSLog(@"Instance Method: %@", isInstance ? @"YES" : @"NO");
    NSLog(@"Class Method: %@", isClass ? @"YES" : @"NO");

    // 获取类型编码
    NSString *encoding = [self getMethodTypeEncoding:selector inClass:targetClass];
    NSLog(@"Type Encoding: %@", encoding ?: @"N/A");

    // 检查是否被替换
    BOOL isReplaced = [self isMethodReplaced:selector inClass:targetClass];
    NSLog(@"Is Replaced: %@", isReplaced ? @"YES" : @"NO");

    if (isReplaced) {
        MethodReplacementInfo *info = [self getReplacementInfo:selector inClass:targetClass];
        NSLog(@"Replacement Type: %ld", (long)info.type);
        NSLog(@"Replaced Date: %@", info.replacedDate);
        NSLog(@"Is Active: %@", info.isActive ? @"YES" : @"NO");
    }

    // 性能统计
    NSInteger execCount = [self getExecutionCount:selector inClass:targetClass];
    NSTimeInterval avgTime = [self getAverageExecutionTime:selector inClass:targetClass];
    NSLog(@"Execution Count: %ld", (long)execCount);
    NSLog(@"Average Execution Time: %.6f seconds", avgTime);

    NSLog(@"========================");
}

#pragma mark - 安全检查

- (BOOL)isSafeToReplace:(SEL)selector inClass:(Class)targetClass {
    NSString *className = NSStringFromClass(targetClass);
    NSString *methodName = NSStringFromSelector(selector);
    
    // 检查不安全的类
    if ([_unsafeClasses containsObject:className]) {
        return NO;
    }
    
    // 检查不安全的方法
    if ([_unsafeMethods containsObject:methodName]) {
        return NO;
    }
    
    return YES;
}

- (NSArray<NSString *> *)getUnsafeClasses {
    return [_unsafeClasses copy];
}

- (NSArray<NSString *> *)getUnsafeMethods {
    return [_unsafeMethods copy];
}

#pragma mark - 调试支持

- (void)enableDebugMode:(BOOL)enabled {
    _debugMode = enabled;
}

- (BOOL)isDebugModeEnabled {
    return _debugMode;
}

- (NSString *)generateDebugReport {
    NSMutableString *report = [NSMutableString string];
    [report appendFormat:@"Method Replacer Debug Report\n"];
    [report appendFormat:@"============================\n"];
    [report appendFormat:@"Initialized: %@\n", _initialized ? @"YES" : @"NO"];
    [report appendFormat:@"Debug Mode: %@\n", _debugMode ? @"YES" : @"NO"];
    [report appendFormat:@"Total Replacements: %ld\n", (long)_replacements.count];
    [report appendFormat:@"\nReplaced Methods:\n"];
    
    for (MethodReplacementInfo *info in _replacements.allValues) {
        [report appendFormat:@"- %@.%@ (%@)\n", 
         info.className, info.methodName, info.isActive ? @"Active" : @"Inactive"];
    }
    
    return [report copy];
}

#pragma mark - 私有辅助方法

- (IMP)createHookImplementation:(NSData *)armCode
                      thumbMode:(BOOL)thumbMode
                       position:(NSString *)position
                    originalIMP:(IMP)originalIMP
                       selector:(SEL)selector
                    targetClass:(Class)targetClass
                   typeEncoding:(const char *)typeEncoding {
    // 创建钩子实现，根据position参数决定执行顺序
    IMP hookIMP = imp_implementationWithBlock(^id(id self, ...) {
        @try {
            // 准备参数
            va_list args;
            va_start(args, self);

            NSMutableArray *arguments = [NSMutableArray arrayWithObject:self];

            // 简化参数处理
            for (int i = 0; i < 4; i++) {
                id arg = va_arg(args, id);
                if (arg) {
                    [arguments addObject:arg];
                } else {
                    break;
                }
            }

            va_end(args);

            id result = nil;
            NSError *execError = nil;

            // 根据position执行不同的钩子逻辑
            if ([position isEqualToString:@"before"]) {
                // 先执行ARM代码，再执行原方法
                [_armInterpreter executeARMCode:armCode
                                      thumbMode:thumbMode
                                     withTarget:self
                                       selector:selector
                                      arguments:arguments
                                          error:&execError];

                if (!execError) {
                    // 调用原方法
                    result = ((id(*)(id, SEL, ...))originalIMP)(self, selector);
                }

            } else if ([position isEqualToString:@"after"]) {
                // 先执行原方法，再执行ARM代码
                result = ((id(*)(id, SEL, ...))originalIMP)(self, selector);

                [_armInterpreter executeARMCode:armCode
                                      thumbMode:thumbMode
                                     withTarget:self
                                       selector:selector
                                      arguments:arguments
                                          error:&execError];

            } else if ([position isEqualToString:@"around"]) {
                // ARM代码完全替换原方法
                result = [_armInterpreter executeARMCode:armCode
                                               thumbMode:thumbMode
                                              withTarget:self
                                                selector:selector
                                               arguments:arguments
                                                   error:&execError];
            }

            if (execError) {
                NSLog(@"Hook execution error: %@", execError.localizedDescription);
            }

            // 更新统计信息
            NSString *key = [self keyForSelector:selector inClass:targetClass];
            [self updateExecutionStats:key];

            return result;

        } @catch (NSException *exception) {
            NSLog(@"Exception in hook implementation: %@", exception.reason);
            return nil;
        }
    });

    return hookIMP;
}

@end
