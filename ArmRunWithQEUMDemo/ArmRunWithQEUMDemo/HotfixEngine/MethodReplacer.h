/*
 * Method Replacer Header for iOS Hot-fix Engine
 * Handles runtime method replacement using Objective-C runtime
 *
 * Copyright (c) 2025
 */

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 方法替换类型
typedef NS_ENUM(NSInteger, MethodReplacementType) {
    MethodReplacementTypeReplace = 0,   // 完全替换
    MethodReplacementTypeSwizzle,       // 方法交换
    MethodReplacementTypeHook           // 方法钩子
};

// 方法替换信息
@interface MethodReplacementInfo : NSObject
@property (nonatomic, strong) NSString *className;
@property (nonatomic, strong) NSString *methodName;
@property (nonatomic, assign) BOOL isInstanceMethod;
@property (nonatomic, assign) MethodReplacementType type;
@property (nonatomic, strong) NSData *originalImplementation;
@property (nonatomic, strong) NSData *replacementImplementation;
@property (nonatomic, strong) NSData *armCode;
@property (nonatomic, assign) BOOL thumbMode;
@property (nonatomic, strong) NSDate *replacedDate;
@property (nonatomic, assign) BOOL isActive;
@property (nonatomic, strong, nullable) NSError *error;
@end

// 方法执行上下文
@interface MethodExecutionContext : NSObject
@property (nonatomic, weak) id target;
@property (nonatomic, assign) SEL selector;
@property (nonatomic, strong) NSArray *arguments;
@property (nonatomic, strong, nullable) id returnValue;
@property (nonatomic, strong, nullable) NSError *error;
@property (nonatomic, assign) NSTimeInterval executionTime;
@end

// 方法替换器主类
@interface MethodReplacer : NSObject

// 单例
+ (instancetype)sharedInstance;

// 初始化
- (BOOL)initialize;
- (void)cleanup;
- (BOOL)isInitialized;

// 方法替换
- (BOOL)replaceMethod:(SEL)selector
              inClass:(Class)targetClass
         withARMCode:(NSData *)armCode
           thumbMode:(BOOL)thumbMode
               error:(NSError * _Nullable * _Nullable)error;

- (BOOL)replaceInstanceMethod:(SEL)selector
                      inClass:(Class)targetClass
                 withARMCode:(NSData *)armCode
                   thumbMode:(BOOL)thumbMode
                       error:(NSError * _Nullable * _Nullable)error;

- (BOOL)replaceClassMethod:(SEL)selector
                   inClass:(Class)targetClass
              withARMCode:(NSData *)armCode
                thumbMode:(BOOL)thumbMode
                    error:(NSError * _Nullable * _Nullable)error;

// 方法交换
- (BOOL)swizzleMethod:(SEL)originalSelector
            withMethod:(SEL)swizzledSelector
               inClass:(Class)targetClass
                 error:(NSError * _Nullable * _Nullable)error;

- (BOOL)swizzleInstanceMethod:(SEL)originalSelector
                   withMethod:(SEL)swizzledSelector
                      inClass:(Class)targetClass
                        error:(NSError * _Nullable * _Nullable)error;

- (BOOL)swizzleClassMethod:(SEL)originalSelector
                withMethod:(SEL)swizzledSelector
                   inClass:(Class)targetClass
                     error:(NSError * _Nullable * _Nullable)error;

// 方法钩子
- (BOOL)hookMethod:(SEL)selector
           inClass:(Class)targetClass
      withARMCode:(NSData *)armCode
        thumbMode:(BOOL)thumbMode
         position:(NSString *)position  // "before", "after", "around"
            error:(NSError * _Nullable * _Nullable)error;

// 方法恢复
- (BOOL)restoreMethod:(SEL)selector
              inClass:(Class)targetClass
                error:(NSError * _Nullable * _Nullable)error;

- (BOOL)restoreAllMethodsInClass:(Class)targetClass
                           error:(NSError * _Nullable * _Nullable)error;

- (BOOL)restoreAllMethods:(NSError * _Nullable * _Nullable)error;

// 查询方法
- (NSArray<MethodReplacementInfo *> *)getReplacedMethods;
- (NSArray<MethodReplacementInfo *> *)getReplacedMethodsInClass:(Class)targetClass;
- (MethodReplacementInfo * _Nullable)getReplacementInfo:(SEL)selector inClass:(Class)targetClass;
- (BOOL)isMethodReplaced:(SEL)selector inClass:(Class)targetClass;

// 方法验证
- (BOOL)isMethodExists:(SEL)selector inClass:(Class)targetClass;
- (BOOL)isInstanceMethod:(SEL)selector inClass:(Class)targetClass;
- (BOOL)isClassMethod:(SEL)selector inClass:(Class)targetClass;
- (NSString * _Nullable)getMethodTypeEncoding:(SEL)selector inClass:(Class)targetClass;

// ARM代码执行
- (id _Nullable)executeARMCode:(NSData *)armCode
                     thumbMode:(BOOL)thumbMode
                    withTarget:(id)target
                      selector:(SEL)selector
                     arguments:(NSArray *)arguments
                         error:(NSError * _Nullable * _Nullable)error;

// 方法调用拦截
- (void)setMethodInterceptor:(id)interceptor;
- (id)getMethodInterceptor;

// 调试支持
- (void)enableDebugMode:(BOOL)enabled;
- (BOOL)isDebugModeEnabled;
- (NSString *)generateDebugReport;
- (void)dumpMethodInfo:(SEL)selector inClass:(Class)targetClass;

// 安全检查
- (BOOL)isSafeToReplace:(SEL)selector inClass:(Class)targetClass;
- (NSArray<NSString *> *)getUnsafeClasses;
- (NSArray<NSString *> *)getUnsafeMethods;

// 性能监控
- (NSTimeInterval)getAverageExecutionTime:(SEL)selector inClass:(Class)targetClass;
- (NSInteger)getExecutionCount:(SEL)selector inClass:(Class)targetClass;
- (void)resetPerformanceStats;

@end

// 方法拦截器协议
@protocol MethodInterceptor <NSObject>
@optional
- (BOOL)shouldInterceptMethod:(SEL)selector inClass:(Class)targetClass;
- (void)willExecuteMethod:(SEL)selector inClass:(Class)targetClass withArguments:(NSArray *)arguments;
- (void)didExecuteMethod:(SEL)selector inClass:(Class)targetClass withReturnValue:(id)returnValue;
- (void)didFailMethod:(SEL)selector inClass:(Class)targetClass withError:(NSError *)error;
@end

// 错误域和错误码
extern NSString * const MethodReplacerErrorDomain;

typedef NS_ENUM(NSInteger, MethodReplacerErrorCode) {
    MethodReplacerErrorCodeGeneral = 4000,
    MethodReplacerErrorCodeNotInitialized = 4001,
    MethodReplacerErrorCodeMethodNotFound = 4002,
    MethodReplacerErrorCodeInvalidClass = 4003,
    MethodReplacerErrorCodeInvalidARMCode = 4004,
    MethodReplacerErrorCodeReplacementFailed = 4005,
    MethodReplacerErrorCodeRestoreFailed = 4006,
    MethodReplacerErrorCodeUnsafeOperation = 4007,
    MethodReplacerErrorCodeExecutionFailed = 4008,
    MethodReplacerErrorCodePermissionDenied = 4009
};

// 通知名称
extern NSString * const MethodReplacerDidInitializeNotification;
extern NSString * const MethodReplacerDidCleanupNotification;
extern NSString * const MethodReplacerMethodDidReplaceNotification;
extern NSString * const MethodReplacerMethodDidRestoreNotification;
extern NSString * const MethodReplacerMethodDidExecuteNotification;
extern NSString * const MethodReplacerErrorNotification;

// 通知用户信息键
extern NSString * const MethodReplacerClassNameKey;
extern NSString * const MethodReplacerMethodNameKey;
extern NSString * const MethodReplacerReplacementInfoKey;
extern NSString * const MethodReplacerExecutionContextKey;
extern NSString * const MethodReplacerErrorKey;

NS_ASSUME_NONNULL_END
