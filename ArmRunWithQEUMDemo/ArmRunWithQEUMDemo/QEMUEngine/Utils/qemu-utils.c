/*
 * QEMU Utility Functions for iOS Hot-fix Engine
 * Simplified version extracted from QEMU
 *
 * Copyright (c) 2025
 */

#include "qemu-utils.h"
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <assert.h>

// 位操作工具函数

// 计算前导零的个数
int clz32(uint32_t val) {
    if (val == 0) {
        return 32;
    }
    
    int count = 0;
    if ((val & 0xFFFF0000) == 0) {
        count += 16;
        val <<= 16;
    }
    if ((val & 0xFF000000) == 0) {
        count += 8;
        val <<= 8;
    }
    if ((val & 0xF0000000) == 0) {
        count += 4;
        val <<= 4;
    }
    if ((val & 0xC0000000) == 0) {
        count += 2;
        val <<= 2;
    }
    if ((val & 0x80000000) == 0) {
        count += 1;
    }
    
    return count;
}

// 计算尾随零的个数
int ctz32(uint32_t val) {
    if (val == 0) {
        return 32;
    }
    
    int count = 0;
    if ((val & 0x0000FFFF) == 0) {
        count += 16;
        val >>= 16;
    }
    if ((val & 0x000000FF) == 0) {
        count += 8;
        val >>= 8;
    }
    if ((val & 0x0000000F) == 0) {
        count += 4;
        val >>= 4;
    }
    if ((val & 0x00000003) == 0) {
        count += 2;
        val >>= 2;
    }
    if ((val & 0x00000001) == 0) {
        count += 1;
    }
    
    return count;
}

// 计算设置位的个数
int popcount32(uint32_t val) {
    val = val - ((val >> 1) & 0x55555555);
    val = (val & 0x33333333) + ((val >> 2) & 0x33333333);
    val = (val + (val >> 4)) & 0x0F0F0F0F;
    val = val + (val >> 8);
    val = val + (val >> 16);
    return val & 0x3F;
}

// 提取位字段
uint32_t extract32(uint32_t value, int start, int length) {
    assert(start >= 0 && length > 0 && length <= 32 - start);
    return (value >> start) & (~0U >> (32 - length));
}

// 存放位字段
uint32_t deposit32(uint32_t value, int start, int length, uint32_t fieldval) {
    uint32_t mask = (~0U >> (32 - length)) << start;
    return (value & ~mask) | ((fieldval << start) & mask);
}

// 符号扩展
int32_t sextract32(uint32_t value, int start, int length) {
    assert(start >= 0 && length > 0 && length <= 32 - start);
    uint32_t ret = extract32(value, start, length);
    if (ret & (1U << (length - 1))) {
        ret |= (~0U << length);
    }
    return (int32_t)ret;
}

// 循环左移
uint32_t rol32(uint32_t word, unsigned int shift) {
    shift &= 31;
    return (word << shift) | (word >> (32 - shift));
}

// 循环右移
uint32_t ror32(uint32_t word, unsigned int shift) {
    shift &= 31;
    return (word >> shift) | (word << (32 - shift));
}

// 字节交换
uint16_t bswap16(uint16_t x) {
    return ((x & 0x00FF) << 8) | ((x & 0xFF00) >> 8);
}

uint32_t bswap32(uint32_t x) {
    return ((x & 0x000000FF) << 24) |
           ((x & 0x0000FF00) << 8) |
           ((x & 0x00FF0000) >> 8) |
           ((x & 0xFF000000) >> 24);
}

uint64_t bswap64(uint64_t x) {
    return ((uint64_t)bswap32((uint32_t)x) << 32) | bswap32((uint32_t)(x >> 32));
}

// 内存工具函数

// 安全的内存分配
void* qemu_malloc(size_t size) {
    if (size == 0) {
        return NULL;
    }
    
    void *ptr = malloc(size);
    if (ptr) {
        memset(ptr, 0, size);
    }
    return ptr;
}

// 安全的内存重新分配
void* qemu_realloc(void *ptr, size_t size) {
    if (size == 0) {
        free(ptr);
        return NULL;
    }
    
    return realloc(ptr, size);
}

// 安全的内存释放
void qemu_free(void *ptr) {
    if (ptr) {
        free(ptr);
    }
}

// 字符串工具函数

// 安全的字符串复制
char* qemu_strdup(const char *str) {
    if (!str) {
        return NULL;
    }
    
    size_t len = strlen(str) + 1;
    char *copy = malloc(len);
    if (copy) {
        memcpy(copy, str, len);
    }
    return copy;
}

// 安全的字符串连接
char* qemu_strcat(const char *str1, const char *str2) {
    if (!str1 && !str2) {
        return NULL;
    }
    if (!str1) {
        return qemu_strdup(str2);
    }
    if (!str2) {
        return qemu_strdup(str1);
    }
    
    size_t len1 = strlen(str1);
    size_t len2 = strlen(str2);
    char *result = malloc(len1 + len2 + 1);
    if (result) {
        memcpy(result, str1, len1);
        memcpy(result + len1, str2, len2 + 1);
    }
    return result;
}

// 数学工具函数

// 计算2的幂次
bool is_power_of_2(uint64_t value) {
    return value != 0 && (value & (value - 1)) == 0;
}

// 向上取整到2的幂次
uint64_t pow2ceil(uint64_t value) {
    if (value == 0) {
        return 1;
    }
    
    value--;
    value |= value >> 1;
    value |= value >> 2;
    value |= value >> 4;
    value |= value >> 8;
    value |= value >> 16;
    value |= value >> 32;
    value++;
    
    return value;
}

// 对齐函数
uint64_t align_up(uint64_t value, uint64_t alignment) {
    if (alignment == 0) {
        return value;
    }
    return (value + alignment - 1) & ~(alignment - 1);
}

uint64_t align_down(uint64_t value, uint64_t alignment) {
    if (alignment == 0) {
        return value;
    }
    return value & ~(alignment - 1);
}

// 错误处理

// 简单的错误报告
void qemu_error(const char *fmt, ...) {
    // 在实际实现中，这里应该使用iOS的日志系统
    // 现在只是一个占位符
    (void)fmt;
}

// 断言失败处理
void qemu_assert_failed(const char *file, int line, const char *func, const char *expr) {
    qemu_error("Assertion failed: %s at %s:%d in %s", expr, file, line, func);
    abort();
}

// 调试工具

// 十六进制转储
void hex_dump(const void *data, size_t size) {
    const uint8_t *bytes = (const uint8_t *)data;
    for (size_t i = 0; i < size; i += 16) {
        // 打印地址
        printf("%08zx: ", i);
        
        // 打印十六进制
        for (size_t j = 0; j < 16; j++) {
            if (i + j < size) {
                printf("%02x ", bytes[i + j]);
            } else {
                printf("   ");
            }
        }
        
        // 打印ASCII
        printf(" |");
        for (size_t j = 0; j < 16 && i + j < size; j++) {
            uint8_t c = bytes[i + j];
            printf("%c", (c >= 32 && c <= 126) ? c : '.');
        }
        printf("|\n");
    }
}
