/*
 * Minimal TCG (Tiny Code Generator) Header for iOS ARM Interpreter
 * Simplified version extracted from QEMU for hot-fix functionality
 *
 * Copyright (c) 2008 Fabrice Bellard
 * iOS Integration (c) 2025
 */

#ifndef TCG_MINIMAL_H
#define TCG_MINIMAL_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// 基本类型定义
typedef uint32_t TCGArg;
typedef int TCGReg;

// TCG类型
typedef enum {
    TCG_TYPE_I32,
    TCG_TYPE_I64,
    TCG_TYPE_PTR,
    TCG_TYPE_COUNT
} TCGType;

// TCG寄存器定义
typedef enum {
    TCG_REG_NONE = -1,
    TCG_REG_R0 = 0,
    TCG_REG_R1,
    TCG_REG_R2,
    TCG_REG_R3,
    TCG_REG_R4,
    TCG_REG_R5,
    TCG_REG_R6,
    TCG_REG_R7,
    TCG_REG_R8,
    TCG_REG_R9,
    TCG_REG_R10,
    TCG_REG_R11,
    TCG_REG_R12,
    TCG_REG_R13,
    TCG_REG_R14,
    TCG_REG_R15,
    TCG_TARGET_NB_REGS
} TCGRegister;

// 临时变量值类型
typedef enum {
    TEMP_VAL_DEAD,
    TEMP_VAL_REG,
    TEMP_VAL_MEM,
    TEMP_VAL_CONST
} TCGTempVal;

// TCG变量类型
typedef struct {
    int index;
} TCGv_i32;

typedef struct {
    int index;
} TCGv_i64;

typedef struct {
    int index;
} TCGv_ptr;

// 常量定义
#define TCG_MAX_TEMPS 512
#define MAX_OPC_PARAM 6
#define TCGV_UNUSED_I32 ((TCGv_i32){-1})
#define TCGV_UNUSED_I64 ((TCGv_i64){-1})

// 宏定义
#define MAKE_TCGV_I32(i) ((TCGv_i32){i})
#define MAKE_TCGV_I64(i) ((TCGv_i64){i})
#define GET_TCGV_I32(t) ((t).index)
#define GET_TCGV_I64(t) ((t).index)

// TCG操作码定义
typedef enum {
    INDEX_op_mov_i32,
    INDEX_op_add_i32,
    INDEX_op_sub_i32,
    INDEX_op_mul_i32,
    INDEX_op_and_i32,
    INDEX_op_or_i32,
    INDEX_op_xor_i32,
    INDEX_op_shl_i32,
    INDEX_op_shr_i32,
    INDEX_op_sar_i32,
    INDEX_op_ld_i32,
    INDEX_op_st_i32,
    INDEX_op_br,
    INDEX_op_brcond_i32,
    INDEX_op_call,
    INDEX_op_exit_tb,
    INDEX_op_goto_tb,
    NB_OPS
} TCGOpcode;

// TCG操作结构
typedef struct TCGOp {
    TCGOpcode opc;
    TCGArg args[MAX_OPC_PARAM];
    uint16_t life;

    // 链表指针
    struct TCGOp *next;
    struct TCGOp *prev;
} TCGOp;

// 临时变量结构
typedef struct TCGTemp {
    TCGType type;
    TCGTempVal val_type;
    TCGReg reg;
    int mem_offset;
    unsigned int fixed_reg:1;
    unsigned int mem_coherent:1;
    unsigned int mem_allocated:1;
    unsigned int temp_local:1;
    unsigned int temp_allocated:1;

    const char *name;
} TCGTemp;

// TCG上下文结构
typedef struct TCGContext {
    uint8_t *code_gen_ptr;
    uint8_t *code_gen_buffer;
    size_t code_gen_buffer_size;

    // 寄存器分配
    uint32_t reserved_regs;

    // 临时变量管理
    int nb_temps;
    TCGTemp temps[TCG_MAX_TEMPS];

    // 操作码缓冲区
    TCGOp *ops;
    int nb_ops;
    int ops_max;

    // 标签管理
    struct TCGLabel *labels;
    int nb_labels;
} TCGContext;

// 标签结构
typedef struct TCGLabel {
    unsigned present:1;
    unsigned id:31;
    union {
        uintptr_t value;
        const TCGOp *op;
    } u;
} TCGLabel;

// 条件码
typedef enum {
    TCG_COND_NEVER  = 0 | 0 | 0 | 0,
    TCG_COND_ALWAYS = 0 | 0 | 0 | 1,
    TCG_COND_EQ     = 8 | 0 | 0 | 0,
    TCG_COND_NE     = 8 | 0 | 0 | 1,
    TCG_COND_LT     = 0 | 0 | 2 | 0,
    TCG_COND_GE     = 0 | 0 | 2 | 1,
    TCG_COND_LE     = 8 | 0 | 2 | 0,
    TCG_COND_GT     = 8 | 0 | 2 | 1,
    TCG_COND_LTU    = 0 | 4 | 0 | 0,
    TCG_COND_GEU    = 0 | 4 | 0 | 1,
    TCG_COND_LEU    = 8 | 4 | 0 | 0,
    TCG_COND_GTU    = 8 | 4 | 0 | 1,
} TCGCond;

// 全局变量
extern TCGContext *tcg_ctx;

// 核心函数声明
bool tcg_minimal_init(size_t code_gen_buffer_size);
void tcg_minimal_cleanup(void);
void tcg_minimal_reset(void);
int tcg_minimal_get_op_count(void);
const TCGOp* tcg_minimal_get_ops(void);
size_t tcg_minimal_generate_code(void);

// 临时变量管理
TCGv_i32 tcg_temp_new_i32(void);
void tcg_temp_free_i32(TCGv_i32 arg);

// 代码生成函数
void tcg_gen_mov_i32(TCGv_i32 ret, TCGv_i32 arg);
void tcg_gen_movi_i32(TCGv_i32 ret, int32_t arg);
void tcg_gen_add_i32(TCGv_i32 ret, TCGv_i32 arg1, TCGv_i32 arg2);
void tcg_gen_sub_i32(TCGv_i32 ret, TCGv_i32 arg1, TCGv_i32 arg2);
void tcg_gen_mul_i32(TCGv_i32 ret, TCGv_i32 arg1, TCGv_i32 arg2);
void tcg_gen_and_i32(TCGv_i32 ret, TCGv_i32 arg1, TCGv_i32 arg2);
void tcg_gen_or_i32(TCGv_i32 ret, TCGv_i32 arg1, TCGv_i32 arg2);
void tcg_gen_xor_i32(TCGv_i32 ret, TCGv_i32 arg1, TCGv_i32 arg2);
void tcg_gen_shl_i32(TCGv_i32 ret, TCGv_i32 arg1, TCGv_i32 arg2);
void tcg_gen_shr_i32(TCGv_i32 ret, TCGv_i32 arg1, TCGv_i32 arg2);
void tcg_gen_sar_i32(TCGv_i32 ret, TCGv_i32 arg1, TCGv_i32 arg2);

// 立即数版本
void tcg_gen_addi_i32(TCGv_i32 ret, TCGv_i32 arg1, int32_t arg2);
void tcg_gen_subi_i32(TCGv_i32 ret, TCGv_i32 arg1, int32_t arg2);
void tcg_gen_muli_i32(TCGv_i32 ret, TCGv_i32 arg1, int32_t arg2);
void tcg_gen_andi_i32(TCGv_i32 ret, TCGv_i32 arg1, int32_t arg2);
void tcg_gen_ori_i32(TCGv_i32 ret, TCGv_i32 arg1, int32_t arg2);
void tcg_gen_xori_i32(TCGv_i32 ret, TCGv_i32 arg1, int32_t arg2);
void tcg_gen_shli_i32(TCGv_i32 ret, TCGv_i32 arg1, int32_t arg2);
void tcg_gen_shri_i32(TCGv_i32 ret, TCGv_i32 arg1, int32_t arg2);
void tcg_gen_sari_i32(TCGv_i32 ret, TCGv_i32 arg1, int32_t arg2);

// 内存操作
void tcg_gen_ld_i32(TCGv_i32 ret, TCGv_ptr arg1, ptrdiff_t arg2);
void tcg_gen_st_i32(TCGv_i32 arg1, TCGv_ptr arg2, ptrdiff_t arg3);

// 控制流
void tcg_gen_br(TCGLabel *label);
void tcg_gen_brcond_i32(TCGCond cond, TCGv_i32 arg1, TCGv_i32 arg2, TCGLabel *label);



// 标签操作
TCGLabel *gen_new_label(void);
void tcg_gen_set_label(TCGLabel *label);

// 函数调用
void tcg_gen_call0(void *func);
void tcg_gen_call1(void *func, TCGv_i32 arg1);
void tcg_gen_call2(void *func, TCGv_i32 arg1, TCGv_i32 arg2);

// 退出翻译块
void tcg_gen_exit_tb(uintptr_t val);
void tcg_gen_goto_tb(unsigned idx);

// 工具函数
static inline bool tcgv_i32_equal(TCGv_i32 a, TCGv_i32 b) {
    return a.index == b.index;
}

static inline bool tcgv_i32_valid(TCGv_i32 a) {
    return a.index >= 0;
}

#ifdef __cplusplus
}
#endif

#endif /* TCG_MINIMAL_H */
