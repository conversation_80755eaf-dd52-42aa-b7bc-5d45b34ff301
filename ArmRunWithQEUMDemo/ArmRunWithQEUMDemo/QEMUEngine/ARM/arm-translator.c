/*
 * ARM Instruction Translator for iOS Hot-fix Engine
 * Simplified version for basic ARM/Thumb instruction support
 *
 * Copyright (c) 2025
 */

#include "arm-translator.h"
#include "../TCI/tci-minimal.h"
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

// ARM指令格式
typedef enum {
    ARM_FORMAT_UNKNOWN = 0,
    ARM_FORMAT_DATA_PROCESSING,
    ARM_FORMAT_LOAD_STORE,
    ARM_FORMAT_BRANCH,
    ARM_FORMAT_MULTIPLY,
    ARM_FORMAT_THUMB_16,
    ARM_FORMAT_THUMB_32
} ARMInstructionFormat;



// ARM翻译上下文
typedef struct {
    uint32_t *instructions;     // 输入的ARM指令
    int instruction_count;      // 指令数量
    TCIInstruction *tci_code;   // 输出的TCI指令
    int tci_count;             // TCI指令数量
    int tci_capacity;          // TCI缓冲区容量
    bool thumb_mode;           // 是否为Thumb模式
} ARMTranslateContext;

// 全局翻译上下文
static ARMTranslateContext translate_ctx;

// 初始化ARM翻译器
bool arm_translator_init(void) {
    memset(&translate_ctx, 0, sizeof(ARMTranslateContext));
    
    // 分配TCI指令缓冲区
    translate_ctx.tci_capacity = 1024;
    translate_ctx.tci_code = malloc(sizeof(TCIInstruction) * translate_ctx.tci_capacity);
    if (!translate_ctx.tci_code) {
        return false;
    }
    
    return true;
}

// 清理ARM翻译器
void arm_translator_cleanup(void) {
    if (translate_ctx.tci_code) {
        free(translate_ctx.tci_code);
        translate_ctx.tci_code = NULL;
    }
    memset(&translate_ctx, 0, sizeof(ARMTranslateContext));
}

// 添加TCI指令到输出缓冲区
static bool add_tci_instruction(TCIInstruction insn) {
    if (translate_ctx.tci_count >= translate_ctx.tci_capacity) {
        // 扩展缓冲区
        int new_capacity = translate_ctx.tci_capacity * 2;
        TCIInstruction *new_buffer = realloc(translate_ctx.tci_code, 
                                           sizeof(TCIInstruction) * new_capacity);
        if (!new_buffer) {
            return false;
        }
        translate_ctx.tci_code = new_buffer;
        translate_ctx.tci_capacity = new_capacity;
    }
    
    translate_ctx.tci_code[translate_ctx.tci_count++] = insn;
    return true;
}

// 解码ARM数据处理指令
static bool decode_arm_data_processing(uint32_t insn) {
    // 提取字段
    uint32_t opcode = (insn >> 21) & 0xF;
    uint32_t rn = (insn >> 16) & 0xF;
    uint32_t rd = (insn >> 12) & 0xF;
    uint32_t operand2 = insn & 0xFFF;

    // 注意：条件码和标志位在简化版本中暂不使用
    // uint32_t cond = (insn >> 28) & 0xF;
    // uint32_t s_bit = (insn >> 20) & 0x1;
    
    // 简化处理：只支持基本操作
    switch (opcode) {
        case 0x4: // ADD
            if (insn & (1 << 25)) {
                // 立即数
                uint32_t imm = operand2 & 0xFF;
                uint32_t rotate = (operand2 >> 8) & 0xF;
                uint32_t value = (imm >> (rotate * 2)) | (imm << (32 - rotate * 2));
                
                // 生成TCI指令：MOV临时寄存器，然后ADD
                add_tci_instruction(tci_mov(15, value)); // 使用R15作为临时寄存器
                add_tci_instruction(tci_add(rd, rn, 15));
            } else {
                // 寄存器
                uint32_t rm = operand2 & 0xF;
                add_tci_instruction(tci_add(rd, rn, rm));
            }
            break;
            
        case 0xD: // MOV
            if (insn & (1 << 25)) {
                // 立即数
                uint32_t imm = operand2 & 0xFF;
                uint32_t rotate = (operand2 >> 8) & 0xF;
                uint32_t value = (imm >> (rotate * 2)) | (imm << (32 - rotate * 2));
                add_tci_instruction(tci_mov(rd, value));
            } else {
                // 寄存器
                uint32_t rm = operand2 & 0xF;
                add_tci_instruction(tci_mov(rd, rm));
            }
            break;
            
        default:
            return false; // 不支持的操作码
    }
    
    return true;
}

// 解码Thumb 16位指令
static bool decode_thumb16_instruction(uint16_t insn) {
    // Thumb指令格式检测
    if ((insn & 0xE000) == 0x0000) {
        // 移位指令 (LSL, LSR, ASR)
        uint32_t opcode = (insn >> 11) & 0x3;
        uint32_t offset = (insn >> 6) & 0x1F;
        uint32_t rm = (insn >> 3) & 0x7;
        uint32_t rd = insn & 0x7;
        
        if (opcode == 0x0 && offset != 0) {
            // LSL (逻辑左移)
            add_tci_instruction(tci_shl(rd, rm, offset));
            return true;
        }
    } else if ((insn & 0xF800) == 0x2000) {
        // MOV立即数指令
        uint32_t rd = (insn >> 8) & 0x7;
        uint32_t imm = insn & 0xFF;
        add_tci_instruction(tci_mov(rd, imm));
        return true;
    } else if ((insn & 0xFE00) == 0x1800) {
        // ADD寄存器指令
        uint32_t rm = (insn >> 6) & 0x7;
        uint32_t rn = (insn >> 3) & 0x7;
        uint32_t rd = insn & 0x7;
        add_tci_instruction(tci_add(rd, rn, rm));
        return true;
    }
    
    return false; // 不支持的指令
}

// 翻译单条ARM指令
static bool translate_arm_instruction(uint32_t insn, bool is_thumb) {
    if (is_thumb) {
        // Thumb模式
        if ((insn & 0xF800F800) == insn) {
            // 16位Thumb指令
            return decode_thumb16_instruction((uint16_t)insn);
        } else {
            // 32位Thumb指令（暂不支持）
            return false;
        }
    } else {
        // ARM模式
        uint32_t cond = (insn >> 28) & 0xF;
        
        // 检查条件码（简化处理，假设总是执行）
        if (cond == ARM_COND_NV) {
            return false; // Never执行
        }
        
        // 根据指令格式分发
        if ((insn & 0x0C000000) == 0x00000000) {
            // 数据处理指令
            return decode_arm_data_processing(insn);
        }
        
        return false; // 其他指令格式暂不支持
    }
}

// 翻译ARM代码到TCI
int arm_translate_to_tci(const uint32_t *arm_code, int count, bool thumb_mode,
                        TCIInstruction **tci_code, int *tci_count) {
    if (!arm_code || count <= 0 || !tci_code || !tci_count) {
        return -1;
    }
    
    // 重置翻译上下文
    translate_ctx.tci_count = 0;
    translate_ctx.thumb_mode = thumb_mode;
    
    // 翻译每条指令
    for (int i = 0; i < count; i++) {
        if (!translate_arm_instruction(arm_code[i], thumb_mode)) {
            // 翻译失败，添加NOP指令
            add_tci_instruction(tci_create_instruction(TCI_OP_NOP, 0, 0, 0, 0));
        }
    }
    
    // 添加退出指令
    add_tci_instruction(tci_exit(0));
    
    // 返回结果
    *tci_code = translate_ctx.tci_code;
    *tci_count = translate_ctx.tci_count;
    
    return 0;
}

// 获取翻译统计信息
void arm_get_translate_stats(ARMTranslateStats *stats) {
    if (stats) {
        stats->total_instructions = translate_ctx.instruction_count;
        stats->translated_instructions = translate_ctx.tci_count;
        stats->thumb_mode = translate_ctx.thumb_mode;
        stats->success_rate = translate_ctx.instruction_count > 0 ? 
            (float)translate_ctx.tci_count / translate_ctx.instruction_count : 0.0f;
    }
}

// 重置翻译器状态
void arm_translator_reset(void) {
    translate_ctx.tci_count = 0;
    translate_ctx.instruction_count = 0;
    translate_ctx.thumb_mode = false;
}
