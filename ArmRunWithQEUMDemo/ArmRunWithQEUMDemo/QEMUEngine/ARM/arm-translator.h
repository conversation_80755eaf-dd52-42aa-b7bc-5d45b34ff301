/*
 * ARM Instruction Translator Header for iOS Hot-fix Engine
 * Simplified version for basic ARM/Thumb instruction support
 *
 * Copyright (c) 2025
 */

#ifndef ARM_TRANSLATOR_H
#define ARM_TRANSLATOR_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// 包含TCI定义
#include "../TCI/tci-minimal.h"

// ARM翻译统计信息
typedef struct {
    int total_instructions;      // 总指令数
    int translated_instructions; // 已翻译指令数
    bool thumb_mode;            // 是否为Thumb模式
    float success_rate;         // 翻译成功率
} ARMTranslateStats;

// ARM寄存器定义
typedef enum {
    ARM_REG_R0 = 0,
    ARM_REG_R1,
    ARM_REG_R2,
    ARM_REG_R3,
    ARM_REG_R4,
    ARM_REG_R5,
    ARM_REG_R6,
    ARM_REG_R7,
    ARM_REG_R8,
    ARM_REG_R9,
    ARM_REG_R10,
    ARM_REG_R11,
    ARM_REG_R12,
    ARM_REG_R13,    // SP (Stack Pointer)
    ARM_REG_R14,    // LR (Link Register)
    ARM_REG_R15,    // PC (Program Counter)
    ARM_REG_COUNT
} ARMRegister;

// ARM寄存器别名
#define ARM_REG_SP  ARM_REG_R13
#define ARM_REG_LR  ARM_REG_R14
#define ARM_REG_PC  ARM_REG_R15

// ARM指令类型 (C版本) - 避免与Objective-C版本冲突
typedef enum {
    ARM_INSN_UNKNOWN = 0,
    ARM_INSN_MOV,           // 数据移动
    ARM_INSN_ADD,           // 加法
    ARM_INSN_SUB,           // 减法
    ARM_INSN_MUL,           // 乘法
    ARM_INSN_AND,           // 按位与
    ARM_INSN_ORR,           // 按位或
    ARM_INSN_EOR,           // 按位异或
    ARM_INSN_LSL,           // 逻辑左移
    ARM_INSN_LSR,           // 逻辑右移
    ARM_INSN_ASR,           // 算术右移
    ARM_INSN_LDR,           // 加载
    ARM_INSN_STR,           // 存储
    ARM_INSN_B,             // 分支
    ARM_INSN_BL,            // 带链接的分支
    ARM_INSN_BX,            // 分支并交换指令集
    ARM_INSN_CMP,           // 比较
    ARM_INSN_TST,           // 测试
    ARM_INSN_NOP            // 无操作
} ARMInstructionTypeC;

// ARM条件码
typedef enum {
    ARM_COND_EQ = 0x0,      // Equal (Z=1)
    ARM_COND_NE = 0x1,      // Not equal (Z=0)
    ARM_COND_CS = 0x2,      // Carry set (C=1)
    ARM_COND_CC = 0x3,      // Carry clear (C=0)
    ARM_COND_MI = 0x4,      // Minus/negative (N=1)
    ARM_COND_PL = 0x5,      // Plus/positive or zero (N=0)
    ARM_COND_VS = 0x6,      // Overflow (V=1)
    ARM_COND_VC = 0x7,      // No overflow (V=0)
    ARM_COND_HI = 0x8,      // Unsigned higher (C=1 and Z=0)
    ARM_COND_LS = 0x9,      // Unsigned lower or same (C=0 or Z=1)
    ARM_COND_GE = 0xA,      // Signed greater than or equal (N=V)
    ARM_COND_LT = 0xB,      // Signed less than (N≠V)
    ARM_COND_GT = 0xC,      // Signed greater than (Z=0 and N=V)
    ARM_COND_LE = 0xD,      // Signed less than or equal (Z=1 or N≠V)
    ARM_COND_AL = 0xE,      // Always
    ARM_COND_NV = 0xF       // Never (deprecated)
} ARMConditionCode;

// ARM指令解析结果 (C版本) - 避免与Objective-C版本冲突
typedef struct {
    ARMInstructionTypeC type;   // 指令类型
    ARMConditionCode cond;      // 条件码
    uint32_t rd;               // 目标寄存器
    uint32_t rn;               // 第一个源寄存器
    uint32_t rm;               // 第二个源寄存器
    uint32_t immediate;        // 立即数
    bool has_immediate;        // 是否包含立即数
    bool sets_flags;           // 是否设置标志位
    uint32_t raw_instruction;  // 原始指令
} ARMInstructionInfoC;

// 核心翻译函数
bool arm_translator_init(void);
void arm_translator_cleanup(void);
void arm_translator_reset(void);

// 主要翻译接口
int arm_translate_to_tci(const uint32_t *arm_code, int count, bool thumb_mode,
                        TCIInstruction **tci_code, int *tci_count);

// 单指令翻译
bool arm_translate_single_instruction(uint32_t instruction, bool thumb_mode,
                                     TCIInstruction *tci_instructions, int *tci_count);

// 指令解析
bool arm_parse_instruction(uint32_t instruction, bool thumb_mode, ARMInstructionInfoC *info);

// 统计和调试
void arm_get_translate_stats(ARMTranslateStats *stats);

// 工具函数
const char* arm_instruction_type_name(ARMInstructionTypeC type);
const char* arm_condition_code_name(ARMConditionCode cond);
const char* arm_register_name(ARMRegister reg);

// 常用ARM指令编码
#define ARM_THUMB_LSL_IMM(rd, rm, imm)  (0x0000 | ((imm) << 6) | ((rm) << 3) | (rd))
#define ARM_THUMB_MOV_IMM(rd, imm)      (0x2000 | ((rd) << 8) | (imm))
#define ARM_THUMB_ADD_REG(rd, rn, rm)   (0x1800 | ((rm) << 6) | ((rn) << 3) | (rd))

// ARM指令编码宏
#define ARM_MOV_IMM(cond, rd, imm)      (((cond) << 28) | (0x3A << 20) | ((rd) << 12) | (imm))
#define ARM_ADD_IMM(cond, rd, rn, imm)  (((cond) << 28) | (0x28 << 20) | ((rn) << 16) | ((rd) << 12) | (imm))

// 错误码
#define ARM_TRANSLATE_SUCCESS           0
#define ARM_TRANSLATE_ERROR_INVALID     -1
#define ARM_TRANSLATE_ERROR_UNSUPPORTED -2
#define ARM_TRANSLATE_ERROR_MEMORY      -3

#ifdef __cplusplus
}
#endif

#endif /* ARM_TRANSLATOR_H */
